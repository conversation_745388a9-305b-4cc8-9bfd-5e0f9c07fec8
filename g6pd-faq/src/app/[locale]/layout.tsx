import type { Metadata } from 'next'
import type React from 'react'
import Navigation from '@/components/layout/Navigation'
import Footer from '@/components/layout/Footer'

const locales = ['zh', 'en']

export function generateStaticParams(): { locale: string }[] {
  return locales.map((locale) => ({ locale }))
}

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params
  const siteTitle = locale === 'zh' ? 'G6PD缺乏症（蚕豆病）FAQ' : 'G6PD Deficiency FAQ'
  const siteDescription = locale === 'zh'
    ? '为G6PD缺乏症患者及家属提供权威、全面的FAQ信息，包括用药指导、饮食建议、症状识别等专业内容。'
    : 'Comprehensive FAQ for G6PD deficiency patients and families'

  return {
    title: {
      template: `%s | ${siteTitle}`,
      default: siteTitle
    },
    description: siteDescription,
    icons: {
      icon: [
        { url: '/icons/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
        { url: '/icons/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
        { url: '/icons/favicon-48x48.png', sizes: '48x48', type: 'image/png' }
      ],
      apple: [
        { url: '/icons/apple-touch-icon.png', sizes: '180x180', type: 'image/png' }
      ],
      other: [
        { rel: 'icon', url: '/favicon.ico' }
      ]
    },
    manifest: '/manifest.json',
    openGraph: {
      title: siteTitle,
      description: siteDescription,
      type: 'website',
      locale: locale,
      alternateLocale: locales.filter(l => l !== locale)
    },
    alternates: {
      languages: {
        'zh': '/zh',
        'en': '/en'
      }
    }
  }
}

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params

  return (
    <div className="min-h-screen flex flex-col">
      <Navigation locale={locale} />
      <main className="flex-1">
        {children}
      </main>
      <Footer locale={locale} />
    </div>
  )
}
