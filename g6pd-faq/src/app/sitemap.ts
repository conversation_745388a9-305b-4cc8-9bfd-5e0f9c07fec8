import { MetadataRoute } from 'next'
import { allFaqData } from '@/lib/faq-data'
import { FAQ } from '@/lib/types'
import fs from 'fs'
import path from 'path'

/**
 * Load programmatic SEO generated FAQs from file system
 */
function loadGeneratedFAQs(): FAQ[] {
  const faqs: FAQ[] = []
  const generatedDir = path.join(process.cwd(), 'src/data/generated-faqs')

  if (!fs.existsSync(generatedDir)) {
    console.warn('Generated FAQs directory not found:', generatedDir)
    return faqs
  }

  try {
    const sources = fs.readdirSync(generatedDir)

    for (const source of sources) {
      const sourcePath = path.join(generatedDir, source)
      if (fs.statSync(sourcePath).isDirectory()) {
        const files = fs.readdirSync(sourcePath)
          .filter(file => file.endsWith('.json') && file !== 'index.json')

        for (const file of files) {
          try {
            const filePath = path.join(sourcePath, file)
            const faqData = JSON.parse(fs.readFileSync(filePath, 'utf8'))
            faqs.push(faqData)
          } catch (error) {
            console.warn(`Failed to load FAQ file: ${file}`, error)
          }
        }
      }
    }
  } catch (error) {
    console.warn('Error loading generated FAQs:', error)
  }

  return faqs
}

export default function sitemap(): MetadataRoute.Sitemap {
  const buildTime = new Date(); // Single timestamp for entire build
  const baseUrl = 'https://g6pd.site'
  const locales = ['zh', 'en']

  const staticPages = [
    '',
    '/faq',
    '/faq/medications',
    '/faq/medications/chinese-medicine',
    '/faq/medications/oral-solutions',
    '/faq/symptoms',
    '/faq/diet',
    '/faq/treatment',
    '/search',
    '/medications',
    '/about'
  ]

  const sitemap: MetadataRoute.Sitemap = []

  // Add static pages for each locale
  locales.forEach(locale => {
    staticPages.forEach(page => {
      sitemap.push({
        url: `${baseUrl}/${locale}${page}`,
        lastModified: buildTime,
        changeFrequency: 'weekly',
        priority: page === '' ? 1.0 : page === '/faq' ? 0.9 : 0.8,
      })
    })
  })

  // Add basic FAQ pages (anchor links)
  locales.forEach(locale => {
    const faqs = allFaqData.filter(faq => faq.locale === locale)
    faqs.forEach(faq => {
      sitemap.push({
        url: `${baseUrl}/${locale}/faq/${faq.category}${faq.subcategory ? `/${faq.subcategory}` : ''}#${faq.id}`,
        lastModified: buildTime,
        changeFrequency: 'monthly',
        priority: 0.6,
      })
    })
  })

  // Add programmatic SEO generated FAQ pages (individual pages)
  const generatedFaqs = loadGeneratedFAQs()
  console.log(`Loading ${generatedFaqs.length} programmatic SEO FAQs into sitemap`)

  generatedFaqs.forEach(faq => {
    // Create individual page URLs for programmatic SEO content
    const locale = faq.locale || 'zh'
    const categoryPath = faq.category === 'medications' ? '/medications' : `/${faq.category}`
    const subcategoryPath = faq.subcategory ? `/${faq.subcategory}` : ''

    // Use slug for individual page URLs
    const faqUrl = `${baseUrl}/${locale}/faq${categoryPath}${subcategoryPath}/${encodeURIComponent(faq.slug)}`

    sitemap.push({
      url: faqUrl,
      lastModified: faq.lastUpdated ? new Date(faq.lastUpdated) : buildTime,
      changeFrequency: 'monthly',
      priority: faq.priority ? Math.min(faq.priority / 100, 0.9) : 0.7, // Convert priority score to sitemap priority
    })
  })

  console.log(`Sitemap generated with ${sitemap.length} URLs (${generatedFaqs.length} programmatic SEO pages)`)
  return sitemap
}
