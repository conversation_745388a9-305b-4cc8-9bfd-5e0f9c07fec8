import React from 'react'
import Head from 'next/head'
import { FAQ } from '../lib/types'
import { seoOptimizer } from '../lib/seo-optimizer'
import { urlStructureManager } from '../lib/url-structure'
import { internalLinkingManager } from '../lib/internal-linking'

interface SEOOptimizedFAQProps {
  faq: FAQ
  locale: string
  allFaqs?: FAQ[]
}

/**
 * SEO优化的FAQ页面组件
 * 包含完整的meta标签、结构化数据和内部链接
 */
export const SEOOptimizedFAQ: React.FC<SEOOptimizedFAQProps> = ({
  faq,
  locale,
  allFaqs = []
}) => {
  // 生成SEO优化数据
  const seoData = seoOptimizer.optimizeFAQPage(faq, locale)
  const urlStructure = urlStructureManager.generateFAQURLStructure(faq, locale)
  const linkingStrategy = internalLinkingManager.generateLinkingStrategy(faq, locale)
  
  const isZh = locale === 'zh'

  return (
    <>
      <Head>
        {/* 基础meta标签 */}
        <title>{faq.seo?.title || `${faq.title} - G6PD缺乏症信息中心`}</title>
        
        {/* 生成的meta标签 */}
        {seoData.metaTags.map((tag, index) => {
          if (tag.name) {
            return <meta key={index} name={tag.name} content={tag.content} />
          } else if (tag.property) {
            return <meta key={index} property={tag.property} content={tag.content} />
          } else if (tag.httpEquiv) {
            return <meta key={index} httpEquiv={tag.httpEquiv} content={tag.content} />
          }
          return null
        })}

        {/* Open Graph标签 */}
        <meta property="og:title" content={seoData.openGraph.title} />
        <meta property="og:description" content={seoData.openGraph.description} />
        <meta property="og:type" content={seoData.openGraph.type} />
        <meta property="og:url" content={seoData.openGraph.url} />
        <meta property="og:site_name" content={seoData.openGraph.siteName} />
        <meta property="og:locale" content={seoData.openGraph.locale} />
        {seoData.openGraph.image && (
          <meta property="og:image" content={seoData.openGraph.image} />
        )}
        {seoData.openGraph.alternateLocales.map(altLocale => (
          <meta key={altLocale} property="og:locale:alternate" content={altLocale} />
        ))}

        {/* Twitter Card标签 */}
        <meta name="twitter:card" content={seoData.twitterCard.card} />
        <meta name="twitter:title" content={seoData.twitterCard.title} />
        <meta name="twitter:description" content={seoData.twitterCard.description} />
        {seoData.twitterCard.image && (
          <meta name="twitter:image" content={seoData.twitterCard.image} />
        )}
        {seoData.twitterCard.site && (
          <meta name="twitter:site" content={seoData.twitterCard.site} />
        )}

        {/* Canonical和多语言链接 */}
        <link rel="canonical" href={seoData.canonicalUrl} />
        {Object.entries(seoData.alternateUrls).map(([lang, url]) => (
          <link key={lang} rel="alternate" hrefLang={lang} href={url} />
        ))}

        {/* 结构化数据 */}
        {seoData.structuredData.map((data, index) => (
          <script
            key={index}
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
          />
        ))}

        {/* 预加载关键资源 */}
        <link rel="preload" href="/fonts/inter.woff2" as="font" type="font/woff2" crossOrigin="" />
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
      </Head>

      <article className="faq-page" itemScope itemType="https://schema.org/FAQPage">
        {/* 面包屑导航 */}
        <nav aria-label={isZh ? '面包屑导航' : 'Breadcrumb navigation'} className="breadcrumb">
          <ol itemScope itemType="https://schema.org/BreadcrumbList">
            {urlStructure.breadcrumbs.map((crumb, index) => (
              <li
                key={index}
                itemScope
                itemType="https://schema.org/ListItem"
                itemProp="itemListElement"
              >
                <a href={crumb.url} itemProp="item">
                  <span itemProp="name">{crumb.name}</span>
                </a>
                <meta itemProp="position" content={crumb.position.toString()} />
              </li>
            ))}
          </ol>
        </nav>

        {/* 主要内容 */}
        <div className="faq-content">
          <header className="faq-header">
            <h1 itemProp="name">{faq.title}</h1>
            
            {/* 分类标签 */}
            <div className="faq-meta">
              {faq.category && (
                <span className="category-tag">
                  {linkingStrategy.categoryLinks.find(link => 
                    link.url.includes(faq.category)
                  )?.text || faq.category}
                </span>
              )}
              {faq.subcategory && (
                <span className="subcategory-tag">
                  {linkingStrategy.categoryLinks.find(link => 
                    link.url.includes(faq.subcategory!)
                  )?.text || faq.subcategory}
                </span>
              )}
            </div>

            {/* 更新时间 */}
            <time dateTime={faq.lastUpdated} className="last-updated">
              {isZh ? '最后更新' : 'Last updated'}: {new Date(faq.lastUpdated).toLocaleDateString(locale)}
            </time>
          </header>

          {/* FAQ主体 */}
          <div itemScope itemType="https://schema.org/Question" itemProp="mainEntity">
            <h2 itemProp="name" className="question">
              {faq.question}
            </h2>
            
            <div itemScope itemType="https://schema.org/Answer" itemProp="acceptedAnswer">
              <div 
                itemProp="text" 
                className="answer"
                dangerouslySetInnerHTML={{ 
                  __html: internalLinkingManager.insertLinksInContent(faq.answer, faq, locale)
                }}
              />
              
              {/* 答案元数据 */}
              <meta itemProp="dateCreated" content={faq.lastUpdated} />
              <div itemScope itemType="https://schema.org/Organization" itemProp="author">
                <meta itemProp="name" content="G6PD缺乏症信息中心" />
              </div>
            </div>
          </div>

          {/* 标签 */}
          {faq.tags && faq.tags.length > 0 && (
            <div className="faq-tags">
              <h3>{isZh ? '相关标签' : 'Related Tags'}</h3>
              <div className="tags-list">
                {linkingStrategy.tagLinks.map((tagLink, index) => (
                  <a key={index} href={tagLink.url} className="tag-link" title={tagLink.title}>
                    {tagLink.text}
                  </a>
                ))}
              </div>
            </div>
          )}

          {/* 相关FAQ */}
          {linkingStrategy.relatedFAQs.length > 0 && (
            <aside className="related-faqs">
              <h3>{isZh ? '相关问题' : 'Related Questions'}</h3>
              <ul>
                {linkingStrategy.relatedFAQs.map((relatedLink, index) => (
                  <li key={index}>
                    <a href={relatedLink.url} title={relatedLink.title}>
                      {relatedLink.text}
                    </a>
                  </li>
                ))}
              </ul>
            </aside>
          )}

          {/* 分类导航 */}
          {linkingStrategy.categoryLinks.length > 0 && (
            <nav className="category-navigation">
              <h3>{isZh ? '浏览更多' : 'Browse More'}</h3>
              <ul>
                {linkingStrategy.categoryLinks.map((categoryLink, index) => (
                  <li key={index}>
                    <a href={categoryLink.url} title={categoryLink.title}>
                      {categoryLink.text}
                    </a>
                  </li>
                ))}
              </ul>
            </nav>
          )}

          {/* 医疗免责声明 */}
          <div className="medical-disclaimer">
            <p>
              {isZh 
                ? '⚠️ 医疗免责声明：本内容仅供参考，不能替代专业医疗建议、诊断或治疗。如有疑问，请咨询合格的医疗专业人员。'
                : '⚠️ Medical Disclaimer: This content is for reference only and cannot replace professional medical advice, diagnosis, or treatment. Please consult qualified medical professionals if you have any questions.'
              }
            </p>
          </div>
        </div>

        {/* SEO性能指标（仅在开发环境显示） */}
        {process.env.NODE_ENV === 'development' && (
          <div className="seo-debug" style={{ 
            position: 'fixed', 
            bottom: '10px', 
            right: '10px', 
            background: '#f0f0f0', 
            padding: '10px', 
            fontSize: '12px',
            maxWidth: '300px',
            zIndex: 9999
          }}>
            <h4>SEO调试信息</h4>
            <p>关键词密度: {seoData.keywordDensity.density}%</p>
            <p>主关键词: {seoData.keywordDensity.focusKeyword}</p>
            <p>内容字数: {seoData.keywordDensity.totalWords}</p>
            <p>内链数量: {linkingStrategy.relatedFAQs.length}</p>
            {seoData.recommendations.length > 0 && (
              <details>
                <summary>SEO建议 ({seoData.recommendations.length})</summary>
                <ul>
                  {seoData.recommendations.slice(0, 3).map((rec, index) => (
                    <li key={index} style={{ color: rec.type === 'error' ? 'red' : rec.type === 'warning' ? 'orange' : 'blue' }}>
                      {rec.message}
                    </li>
                  ))}
                </ul>
              </details>
            )}
          </div>
        )}
      </article>

      <style jsx>{`
        .faq-page {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
          line-height: 1.6;
        }

        .breadcrumb ol {
          display: flex;
          list-style: none;
          padding: 0;
          margin: 0 0 20px 0;
        }

        .breadcrumb li:not(:last-child)::after {
          content: ' > ';
          margin: 0 8px;
          color: #666;
        }

        .breadcrumb a {
          color: #0066cc;
          text-decoration: none;
        }

        .breadcrumb a:hover {
          text-decoration: underline;
        }

        .faq-header h1 {
          font-size: 2rem;
          margin-bottom: 10px;
          color: #333;
        }

        .faq-meta {
          margin-bottom: 10px;
        }

        .category-tag, .subcategory-tag {
          display: inline-block;
          background: #e3f2fd;
          color: #1976d2;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 0.875rem;
          margin-right: 8px;
        }

        .last-updated {
          color: #666;
          font-size: 0.875rem;
        }

        .question {
          font-size: 1.5rem;
          color: #2c5aa0;
          margin: 30px 0 20px 0;
        }

        .answer {
          margin-bottom: 30px;
        }

        .answer :global(a) {
          color: #0066cc;
          text-decoration: none;
        }

        .answer :global(a:hover) {
          text-decoration: underline;
        }

        .faq-tags h3, .related-faqs h3, .category-navigation h3 {
          font-size: 1.25rem;
          margin: 30px 0 15px 0;
          color: #333;
        }

        .tags-list {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }

        .tag-link {
          background: #f5f5f5;
          color: #666;
          padding: 4px 8px;
          border-radius: 4px;
          text-decoration: none;
          font-size: 0.875rem;
        }

        .tag-link:hover {
          background: #e0e0e0;
        }

        .related-faqs ul, .category-navigation ul {
          list-style: none;
          padding: 0;
        }

        .related-faqs li, .category-navigation li {
          margin-bottom: 8px;
        }

        .related-faqs a, .category-navigation a {
          color: #0066cc;
          text-decoration: none;
        }

        .related-faqs a:hover, .category-navigation a:hover {
          text-decoration: underline;
        }

        .medical-disclaimer {
          background: #fff3cd;
          border: 1px solid #ffeaa7;
          border-radius: 4px;
          padding: 15px;
          margin-top: 30px;
        }

        .medical-disclaimer p {
          margin: 0;
          color: #856404;
        }

        @media (max-width: 768px) {
          .faq-page {
            padding: 15px;
          }

          .faq-header h1 {
            font-size: 1.5rem;
          }

          .question {
            font-size: 1.25rem;
          }

          .breadcrumb ol {
            flex-wrap: wrap;
          }
        }
      `}</style>
    </>
  )
}

export default SEOOptimizedFAQ
