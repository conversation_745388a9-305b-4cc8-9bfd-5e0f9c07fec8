'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'

interface NavigationProps {
  locale: string
}

export default function Navigation({ locale }: NavigationProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const isZh = locale === 'zh'

  const navigation = [
    {
      name: isZh ? '首页' : 'Home',
      href: `/${locale}`,
    },
    {
      name: isZh ? '常见问题' : 'FAQ',
      href: `/${locale}/faq`,
      children: [
        {
          name: isZh ? '中药禁忌' : 'Chinese Medicine',
          href: `/${locale}/faq/medications/chinese-medicine`,
        },
        {
          name: isZh ? '口服液安全' : 'Oral Solutions',
          href: `/${locale}/faq/medications/oral-solutions`,
        },
        {
          name: isZh ? '症状识别' : 'Symptoms',
          href: `/${locale}/faq/symptoms`,
        },
        {
          name: isZh ? '饮食指导' : 'Diet Guide',
          href: `/${locale}/faq/diet`,
        },
        {
          name: isZh ? '治疗方案' : 'Treatment',
          href: `/${locale}/faq/treatment`,
        },
      ],
    },
    {
      name: isZh ? '用药指导' : 'Medication Guide',
      href: `/${locale}/medications`,
    },
    {
      name: isZh ? '搜索' : 'Search',
      href: `/${locale}/search`,
    },
  ]

  const otherLocale = locale === 'zh' ? 'en' : 'zh'
  const otherLocaleName = locale === 'zh' ? 'English' : '中文'

  return (
    <nav className="bg-white shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link href={`/${locale}`} className="flex-shrink-0">
              <div className="flex items-center">
                <Image
                  src="/icons/favicon-48x48.png"
                  alt="G6PD Logo"
                  width={32}
                  height={32}
                  className="rounded-lg"
                />
                <span className="ml-2 text-xl font-bold text-gray-900">
                  {isZh ? 'G6PD指导' : 'G6PD Guide'}
                </span>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => (
              <div key={item.name} className="relative group">
                <Link
                  href={item.href}
                  className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors"
                >
                  {item.name}
                </Link>
                {item.children && (
                  <div className="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                    <div className="py-1">
                      {item.children.map((child) => (
                        <Link
                          key={child.name}
                          href={child.href}
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                        >
                          {child.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
            
            {/* Language Switcher */}
            <Link
              href={`/${otherLocale}`}
              className="text-gray-500 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors border border-gray-300 rounded-md"
            >
              {otherLocaleName}
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600 p-2 rounded-md hover:bg-gray-100 transition-colors"
              aria-label={isMenuOpen ? (isZh ? '关闭菜单' : 'Close menu') : (isZh ? '打开菜单' : 'Open menu')}
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-gray-200">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white shadow-lg">
              {navigation.map((item) => (
                <div key={item.name}>
                  <Link
                    href={item.href}
                    className="text-gray-700 hover:text-blue-600 hover:bg-blue-50 block px-4 py-3 text-base font-medium rounded-md transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                  {item.children && (
                    <div className="pl-4 space-y-1 mt-1">
                      {item.children.map((child) => (
                        <Link
                          key={child.name}
                          href={child.href}
                          className="text-gray-600 hover:text-blue-600 hover:bg-blue-50 block px-4 py-2 text-sm rounded-md transition-colors"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          {child.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ))}
              <div className="border-t border-gray-200 mt-4 pt-4">
                <Link
                  href={`/${otherLocale}`}
                  className="text-gray-500 hover:text-blue-600 hover:bg-blue-50 block px-4 py-3 text-base font-medium rounded-md transition-colors border border-gray-300"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {otherLocaleName}
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
