import { FAQ } from './types'

/**
 * URL结构管理器
 * 负责生成SEO友好的URL结构和导航路径
 */

export interface URLStructure {
  path: string
  breadcrumbs: Breadcrumb[]
  canonicalUrl: string
  alternateUrls: Record<string, string>
  structuredData: any
}

export interface Breadcrumb {
  name: string
  url: string
  position: number
}

export interface NavigationItem {
  id: string
  title: string
  url: string
  children?: NavigationItem[]
  count?: number
  priority: number
}

export class URLStructureManager {
  private static instance: URLStructureManager
  private baseUrl: string = 'https://g6pd.site'
  
  static getInstance(): URLStructureManager {
    if (!URLStructureManager.instance) {
      URLStructureManager.instance = new URLStructureManager()
    }
    return URLStructureManager.instance
  }

  /**
   * 生成FAQ页面的URL结构
   */
  generateFAQURLStructure(faq: FAQ, locale: string = 'zh'): URLStructure {
    const path = this.generateFAQPath(faq, locale)
    const breadcrumbs = this.generateBreadcrumbs(faq, locale)
    const canonicalUrl = `${this.baseUrl}${path}`
    
    return {
      path,
      breadcrumbs,
      canonicalUrl,
      alternateUrls: this.generateAlternateUrls(path),
      structuredData: this.generateStructuredData(faq, canonicalUrl)
    }
  }

  /**
   * 生成FAQ页面路径
   */
  private generateFAQPath(faq: FAQ, locale: string): string {
    const localePrefix = locale === 'zh' ? '/zh' : '/en'
    const categoryPath = this.getCategoryPath(faq.category)
    const subcategoryPath = faq.subcategory ? `/${faq.subcategory}` : ''
    
    return `${localePrefix}/faq${categoryPath}${subcategoryPath}/${faq.slug}`
  }

  /**
   * 获取分类路径
   */
  private getCategoryPath(category: string): string {
    const categoryPaths: Record<string, string> = {
      'medications': '/medications',
      'symptoms': '/symptoms', 
      'diet': '/diet',
      'lifestyle': '/lifestyle',
      'emergency': '/emergency',
      'pediatric': '/pediatric'
    }
    
    return categoryPaths[category] || '/general'
  }

  /**
   * 生成面包屑导航
   */
  private generateBreadcrumbs(faq: FAQ, locale: string): Breadcrumb[] {
    const isZh = locale === 'zh'
    const breadcrumbs: Breadcrumb[] = []
    
    // 首页
    breadcrumbs.push({
      name: isZh ? '首页' : 'Home',
      url: locale === 'zh' ? '/zh' : '/en',
      position: 1
    })
    
    // FAQ首页
    breadcrumbs.push({
      name: isZh ? '常见问题' : 'FAQ',
      url: `/${locale}/faq`,
      position: 2
    })
    
    // 分类页面
    const categoryName = this.getCategoryName(faq.category, isZh)
    breadcrumbs.push({
      name: categoryName,
      url: `/${locale}/faq${this.getCategoryPath(faq.category)}`,
      position: 3
    })
    
    // 子分类页面（如果存在）
    if (faq.subcategory) {
      const subcategoryName = this.getSubcategoryName(faq.subcategory, isZh)
      breadcrumbs.push({
        name: subcategoryName,
        url: `/${locale}/faq${this.getCategoryPath(faq.category)}/${faq.subcategory}`,
        position: 4
      })
    }
    
    // 当前页面
    breadcrumbs.push({
      name: faq.title,
      url: this.generateFAQPath(faq, locale),
      position: breadcrumbs.length + 1
    })
    
    return breadcrumbs
  }

  /**
   * 获取分类名称
   */
  private getCategoryName(category: string, isZh: boolean): string {
    const categoryNames: Record<string, { zh: string; en: string }> = {
      'medications': { zh: '药物相关', en: 'Medications' },
      'symptoms': { zh: '症状表现', en: 'Symptoms' },
      'diet': { zh: '饮食指南', en: 'Diet' },
      'lifestyle': { zh: '生活方式', en: 'Lifestyle' },
      'emergency': { zh: '紧急情况', en: 'Emergency' },
      'pediatric': { zh: '儿童相关', en: 'Pediatric' }
    }
    
    return categoryNames[category]?.[isZh ? 'zh' : 'en'] || (isZh ? '其他' : 'Other')
  }

  /**
   * 获取子分类名称
   */
  private getSubcategoryName(subcategory: string, isZh: boolean): string {
    const subcategoryNames: Record<string, { zh: string; en: string }> = {
      'chinese-medicine': { zh: '中药', en: 'Chinese Medicine' },
      'western-medicine': { zh: '西药', en: 'Western Medicine' },
      'oral-solutions': { zh: '口服液', en: 'Oral Solutions' },
      'antibiotics': { zh: '抗生素', en: 'Antibiotics' },
      'acute-symptoms': { zh: '急性症状', en: 'Acute Symptoms' },
      'chronic-symptoms': { zh: '慢性症状', en: 'Chronic Symptoms' },
      'foods-to-avoid': { zh: '禁忌食物', en: 'Foods to Avoid' },
      'safe-foods': { zh: '安全食物', en: 'Safe Foods' },
      'infant-care': { zh: '婴儿护理', en: 'Infant Care' },
      'child-care': { zh: '儿童护理', en: 'Child Care' }
    }
    
    return subcategoryNames[subcategory]?.[isZh ? 'zh' : 'en'] || subcategory
  }

  /**
   * 生成多语言URL
   */
  private generateAlternateUrls(path: string): Record<string, string> {
    const zhPath = path.replace(/^\/en/, '/zh')
    const enPath = path.replace(/^\/zh/, '/en')
    
    return {
      'zh': `${this.baseUrl}${zhPath}`,
      'en': `${this.baseUrl}${enPath}`
    }
  }

  /**
   * 生成结构化数据
   */
  private generateStructuredData(faq: FAQ, canonicalUrl: string): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      'mainEntity': {
        '@type': 'Question',
        'name': faq.question,
        'acceptedAnswer': {
          '@type': 'Answer',
          'text': faq.answer
        }
      },
      'url': canonicalUrl,
      'datePublished': faq.lastUpdated,
      'dateModified': faq.lastUpdated,
      'author': {
        '@type': 'Organization',
        'name': 'G6PD缺乏症信息中心'
      },
      'publisher': {
        '@type': 'Organization',
        'name': 'G6PD缺乏症信息中心',
        'url': this.baseUrl
      }
    }
  }

  /**
   * 生成分类导航结构
   */
  generateCategoryNavigation(faqs: FAQ[], locale: string = 'zh'): NavigationItem[] {
    const isZh = locale === 'zh'
    const navigation: NavigationItem[] = []
    
    // 按分类分组
    const categoryGroups = this.groupFAQsByCategory(faqs)
    
    Object.entries(categoryGroups).forEach(([category, categoryFaqs]) => {
      const categoryItem: NavigationItem = {
        id: category,
        title: this.getCategoryName(category, isZh),
        url: `/${locale}/faq${this.getCategoryPath(category)}`,
        count: categoryFaqs.length,
        priority: this.getCategoryPriority(category),
        children: []
      }
      
      // 按子分类分组
      const subcategoryGroups = this.groupFAQsBySubcategory(categoryFaqs)
      
      Object.entries(subcategoryGroups).forEach(([subcategory, subcategoryFaqs]) => {
        if (subcategory && subcategory !== 'undefined') {
          categoryItem.children!.push({
            id: `${category}-${subcategory}`,
            title: this.getSubcategoryName(subcategory, isZh),
            url: `/${locale}/faq${this.getCategoryPath(category)}/${subcategory}`,
            count: subcategoryFaqs.length,
            priority: this.getSubcategoryPriority(subcategory)
          })
        }
      })
      
      // 按优先级排序子分类
      if (categoryItem.children) {
        categoryItem.children.sort((a, b) => b.priority - a.priority)
      }
      
      navigation.push(categoryItem)
    })
    
    // 按优先级排序分类
    navigation.sort((a, b) => b.priority - a.priority)
    
    return navigation
  }

  /**
   * 按分类分组FAQ
   */
  private groupFAQsByCategory(faqs: FAQ[]): Record<string, FAQ[]> {
    return faqs.reduce((groups, faq) => {
      const category = faq.category || 'general'
      if (!groups[category]) {
        groups[category] = []
      }
      groups[category].push(faq)
      return groups
    }, {} as Record<string, FAQ[]>)
  }

  /**
   * 按子分类分组FAQ
   */
  private groupFAQsBySubcategory(faqs: FAQ[]): Record<string, FAQ[]> {
    return faqs.reduce((groups, faq) => {
      const subcategory = faq.subcategory || 'general'
      if (!groups[subcategory]) {
        groups[subcategory] = []
      }
      groups[subcategory].push(faq)
      return groups
    }, {} as Record<string, FAQ[]>)
  }

  /**
   * 获取分类优先级
   */
  private getCategoryPriority(category: string): number {
    const priorities: Record<string, number> = {
      'medications': 100,
      'emergency': 90,
      'symptoms': 80,
      'pediatric': 70,
      'diet': 60,
      'lifestyle': 50
    }
    
    return priorities[category] || 10
  }

  /**
   * 获取子分类优先级
   */
  private getSubcategoryPriority(subcategory: string): number {
    const priorities: Record<string, number> = {
      'chinese-medicine': 100,
      'western-medicine': 90,
      'oral-solutions': 80,
      'antibiotics': 70,
      'acute-symptoms': 100,
      'chronic-symptoms': 80,
      'infant-care': 100,
      'child-care': 90,
      'foods-to-avoid': 100,
      'safe-foods': 80
    }
    
    return priorities[subcategory] || 10
  }

  /**
   * 生成站点地图URL列表
   */
  generateSitemapUrls(faqs: FAQ[]): Array<{ url: string; lastmod: string; priority: number; changefreq: string }> {
    const urls: Array<{ url: string; lastmod: string; priority: number; changefreq: string }> = []
    
    faqs.forEach(faq => {
      // 中文版本
      const zhStructure = this.generateFAQURLStructure(faq, 'zh')
      urls.push({
        url: zhStructure.canonicalUrl,
        lastmod: faq.lastUpdated,
        priority: this.calculateURLPriority(faq),
        changefreq: 'monthly'
      })
      
      // 英文版本
      const enStructure = this.generateFAQURLStructure(faq, 'en')
      urls.push({
        url: enStructure.canonicalUrl,
        lastmod: faq.lastUpdated,
        priority: this.calculateURLPriority(faq),
        changefreq: 'monthly'
      })
    })
    
    return urls
  }

  /**
   * 计算URL优先级
   */
  private calculateURLPriority(faq: FAQ): number {
    let priority = 0.5 // 基础优先级
    
    // 根据搜索量调整
    if (faq.searchVolume && faq.searchVolume > 100000) {
      priority += 0.3
    } else if (faq.searchVolume && faq.searchVolume > 10000) {
      priority += 0.2
    } else if (faq.searchVolume && faq.searchVolume > 1000) {
      priority += 0.1
    }
    
    // 根据分类调整
    if (faq.category === 'emergency' || faq.category === 'medications') {
      priority += 0.2
    }
    
    // 根据质量评分调整
    if (faq.qualityScore && faq.qualityScore > 90) {
      priority += 0.1
    }
    
    return Math.min(priority, 1.0)
  }
}

// 导出单例实例
export const urlStructureManager = URLStructureManager.getInstance()
