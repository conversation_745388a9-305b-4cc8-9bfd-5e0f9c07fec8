import { 
  FAQ, 
  LongTailKeyword, 
  ContentTemplate, 
  ContentVariant,
  SEOMetadata 
} from './types'
import { programmaticSEOManager } from './programmatic-seo'

/**
 * 内容自动生成引擎
 * 基于长尾关键词和模板生成差异化的FAQ内容
 */
export class ContentGenerator {
  private static instance: ContentGenerator
  private templates: Map<string, ContentTemplate> = new Map()

  static getInstance(): ContentGenerator {
    if (!ContentGenerator.instance) {
      ContentGenerator.instance = new ContentGenerator()
    }
    return ContentGenerator.instance
  }

  /**
   * 初始化内容模板
   */
  async initializeTemplates(): Promise<void> {
    // 中药相关模板
    const chineseMedicineTemplate: ContentTemplate = {
      id: 'chinese-medicine-faq',
      name: '中药FAQ模板',
      description: '用于生成中药相关FAQ的模板',
      category: 'medications',
      template: this.getChineseMedicineTemplate(),
      variables: [
        {
          name: 'keyword',
          type: 'string',
          description: '目标关键词',
          required: true
        },
        {
          name: 'medicines',
          type: 'array',
          description: '相关中药列表',
          required: true,
          defaultValue: ['薄荷', '金银花', '黄连', '大黄', '麻黄', '茵陈']
        },
        {
          name: 'symptoms',
          type: 'array',
          description: '可能症状',
          required: false,
          defaultValue: ['溶血性贫血', '黄疸', '血红蛋白下降']
        }
      ],
      seoTemplate: {
        titleTemplate: '{{keyword}} - G6PD缺乏症用药指导',
        descriptionTemplate: '详细解答{{keyword}}相关问题，为G6PD缺乏症患者提供专业的中药使用指导和安全建议。',
        keywordsTemplate: ['{{keyword}}', 'G6PD缺乏症', '蚕豆病', '中药禁忌', '用药安全']
      },
      locale: 'zh'
    }

    // 口服液相关模板
    const oralSolutionTemplate: ContentTemplate = {
      id: 'oral-solution-faq',
      name: '口服液FAQ模板',
      description: '用于生成口服液相关FAQ的模板',
      category: 'medications',
      template: this.getOralSolutionTemplate(),
      variables: [
        {
          name: 'keyword',
          type: 'string',
          description: '目标关键词',
          required: true
        },
        {
          name: 'medicationName',
          type: 'string',
          description: '具体药物名称',
          required: true
        },
        {
          name: 'safetyLevel',
          type: 'string',
          description: '安全等级',
          required: true,
          defaultValue: 'prohibited'
        }
      ],
      seoTemplate: {
        titleTemplate: '{{keyword}} - 口服液用药安全指导',
        descriptionTemplate: '{{medicationName}}对G6PD缺乏症患者是否安全？专业医师为您详细解答{{keyword}}相关问题。',
        keywordsTemplate: ['{{keyword}}', '{{medicationName}}', 'G6PD缺乏症', '口服液', '用药安全']
      },
      locale: 'zh'
    }

    this.templates.set(chineseMedicineTemplate.id, chineseMedicineTemplate)
    this.templates.set(oralSolutionTemplate.id, oralSolutionTemplate)
  }

  /**
   * 根据关键词生成FAQ内容
   */
  async generateFAQFromKeyword(keyword: LongTailKeyword, templateId: string): Promise<FAQ> {
    const template = this.templates.get(templateId)
    if (!template) {
      throw new Error(`模板不存在: ${templateId}`)
    }

    // 确定内容变体类型
    const variantType = this.determineContentVariant(keyword.keyword)
    
    // 生成基础内容
    const content = this.generateContent(keyword, template, variantType)
    
    // 生成SEO元数据
    const seoMetadata = programmaticSEOManager.generateSEOMetadata(
      keyword.keyword,
      content.content,
      template.locale
    )

    // 创建FAQ对象
    const faq: FAQ = {
      id: this.generateFAQId(keyword.keyword),
      slug: this.generateSlug(keyword.keyword),
      title: content.title || `${keyword.keyword}相关问题`,
      question: content.title || `${keyword.keyword}相关问题`,
      answer: content.content || `关于${keyword.keyword}的详细信息，请咨询医生获取专业建议。`,
      shortAnswer: content.content ? (content.content.length > 100 ? content.content.substring(0, 100) + '...' : content.content) : '请咨询医生获取专业建议。',
      category: template.category,
      subcategory: this.determineSubcategory(keyword.keyword),
      tags: this.generateTags(keyword),
      difficulty: this.determineDifficulty(keyword),
      priority: this.calculatePriority(keyword),
      relatedFaqs: [],
      lastUpdated: new Date().toISOString(),
      medicalReview: false,
      locale: template.locale,
      seo: seoMetadata,
      longTailKeywords: [keyword],
      searchVolume: keyword.searchVolume,
      competitionLevel: this.determineCompetitionLevel(keyword.difficulty),
      contentVariants: [content],
      autoGenerated: true,
      generationSource: keyword.source,
      qualityScore: this.calculateQualityScore(content, keyword)
    }

    return faq
  }

  /**
   * 生成内容变体
   */
  private generateContent(keyword: LongTailKeyword, template: ContentTemplate, variantType: string): ContentVariant {
    const variables = this.extractVariables(keyword.keyword)
    let content = template.template

    // 替换模板变量
    template.variables.forEach(variable => {
      const value = variables[variable.name] || variable.defaultValue || ''
      const placeholder = `{{${variable.name}}}`
      const replacementValue = Array.isArray(value) ? value.join('、') : String(value)
      content = content.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), replacementValue)
    })

    // 根据变体类型调整内容
    content = this.adjustContentByVariant(content, variantType, keyword)

    const parts = this.parseGeneratedContent(content)

    return {
      id: this.generateVariantId(),
      type: variantType as any,
      title: parts.question || `${keyword.keyword}相关问题`,
      description: `${variantType}角度的内容变体`,
      targetKeywords: [keyword.keyword, ...keyword.variations],
      content: parts.answer || `关于${keyword.keyword}的详细信息，请咨询医生获取专业建议。`,
      seoScore: this.calculateSEOScore(parts.answer || '', keyword.keyword)
    }
  }

  /**
   * 确定内容变体类型
   */
  private determineContentVariant(keyword: string): string {
    if (keyword.includes('婴儿') || keyword.includes('宝宝') || keyword.includes('小儿')) {
      return 'audience' // 受众差异化
    }
    if (keyword.includes('什么') || keyword.includes('哪些')) {
      return 'depth' // 深度差异化
    }
    if (keyword.includes('能不能') || keyword.includes('可以')) {
      return 'angle' // 角度差异化
    }
    return 'format' // 格式差异化
  }

  /**
   * 从关键词中提取变量
   */
  private extractVariables(keyword: string): Record<string, any> {
    const variables: Record<string, any> = {
      keyword: keyword
    }

    // 提取药物名称
    const medicineMatch = keyword.match(/(双黄连|抗病毒|复方甘草|茵栀黄|鱼腥草|金振|蓝芩|咳喘灵|保济|四磨汤)/)
    if (medicineMatch) {
      variables.medicationName = medicineMatch[1]
    }

    // 提取中药成分
    const chineseMedicineMatch = keyword.match(/(薄荷|金银花|黄连|大黄|麻黄|茵陈|黄芩|甘草|款冬花|知母|儿茶)/)
    if (chineseMedicineMatch) {
      variables.medicines = [chineseMedicineMatch[1]]
    }

    return variables
  }

  /**
   * 根据变体类型调整内容
   */
  private adjustContentByVariant(content: string, variantType: string, keyword: LongTailKeyword): string {
    if (!content || typeof content !== 'string') {
      return content || ''
    }

    switch (variantType) {
      case 'audience':
        if (keyword.keyword.includes('婴儿') || keyword.keyword.includes('宝宝')) {
          content = content.replace(/患者/g, '婴幼儿患者')
          content = content.replace(/使用前/g, '给宝宝使用前')
          content += '\n\n特别提醒：婴幼儿的药物代谢能力较弱，G6PD缺乏症的症状可能更加严重，家长应格外谨慎。'
        }
        break
      case 'depth':
        content += '\n\n详细说明：\n1. 药物成分分析\n2. 作用机制解释\n3. 风险评估\n4. 替代方案建议'
        break
      case 'angle':
        if (keyword.keyword.includes('能不能') || keyword.keyword.includes('可以')) {
          content = '从安全性角度分析：\n' + content
          content += '\n\n建议：在医生指导下谨慎使用，定期监测血常规。'
        }
        break
    }
    return content
  }

  /**
   * 解析生成的内容
   */
  private parseGeneratedContent(content: string): { question: string; answer: string; shortAnswer?: string } {
    if (!content || typeof content !== 'string') {
      return { question: '未知问题', answer: '内容生成失败', shortAnswer: '内容生成失败' }
    }

    const lines = content.split('\n').filter(line => line.trim())

    // 第一行作为问题
    const question = lines[0]?.replace(/^#+\s*/, '') || '未知问题'

    // 其余内容作为答案
    const answer = lines.slice(1).join('\n') || '暂无内容'

    // 生成简短答案（前100字符）
    const shortAnswer = answer.length > 100 ? answer.substring(0, 100) + '...' : answer

    return { question, answer, shortAnswer }
  }

  /**
   * 中药模板内容
   */
  private getChineseMedicineTemplate(): string {
    return `# {{keyword}}

G6PD缺乏症（蚕豆病）患者在使用中药时需要特别谨慎。以下是关于{{keyword}}的详细解答：

## 禁用中药成分

G6PD缺乏症患者应避免使用以下中药成分：
{{medicines}}

这些中药成分可能引起溶血性贫血发作，导致以下症状：
{{symptoms}}

## 安全建议

1. **咨询专业医师**：使用任何中药前，请务必告知医生您的G6PD缺乏症状况
2. **仔细阅读成分**：检查中药制剂的成分表，避免含有禁用成分的药物
3. **监测症状**：如出现疲劳、面色苍白、黄疸等症状，应立即停药就医
4. **选择替代方案**：在医生指导下选择安全的替代治疗方案

## 紧急情况处理

如果误用禁忌中药出现溶血症状，应：
- 立即停止用药
- 大量饮水
- 尽快就医
- 告知医生具体用药情况

请记住，G6PD缺乏症患者的用药安全至关重要，任何疑问都应咨询专业医师。`
  }

  /**
   * 口服液模板内容
   */
  private getOralSolutionTemplate(): string {
    return `# {{keyword}}

关于{{medicationName}}与G6PD缺乏症的安全性问题，以下是详细解答：

## 安全性评估

{{medicationName}}对于G6PD缺乏症患者的安全性需要谨慎评估。该药物可能含有以下风险成分：

1. **活性成分分析**：需要检查是否含有可能引起溶血的成分
2. **辅料成分**：某些辅料也可能对G6PD缺乏症患者有风险
3. **个体差异**：不同患者的敏感性可能不同

## 使用建议

### 如果必须使用：
- 在医生严密监督下使用
- 从最小剂量开始
- 密切观察症状变化
- 定期检查血常规

### 替代方案：
- 咨询医生其他安全的治疗选择
- 考虑非药物治疗方法
- 选择已确认安全的同类药物

## 症状监测

使用期间应注意以下症状：
- 疲劳乏力
- 面色苍白
- 黄疸（皮肤、眼白发黄）
- 尿液颜色加深
- 腹痛、恶心

一旦出现上述症状，应立即停药并就医。

## 专业建议

G6PD缺乏症患者用药需要个体化评估，建议：
1. 保留G6PD缺乏症检测报告
2. 就医时主动告知病史
3. 建立个人用药档案
4. 定期复查相关指标

安全用药是G6PD缺乏症管理的重要环节，请务必在专业医师指导下用药。`
  }

  // 辅助方法
  private generateFAQId(keyword: string): string {
    return `faq-${Date.now()}-${keyword.replace(/\s+/g, '-').toLowerCase()}`
  }

  private generateSlug(keyword: string): string {
    return keyword
      .replace(/\s+/g, '-')
      .toLowerCase()
      .replace(/[^\u4e00-\u9fa5a-z0-9\-]/g, '') // 保留中文、英文、数字和连字符
      .replace(/^-+|-+$/g, '') // 移除开头和结尾的连字符
      .replace(/-+/g, '-') // 合并多个连字符
      || 'untitled' // 如果结果为空，使用默认值
  }

  private generateVariantId(): string {
    return `variant-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  private determineSubcategory(keyword: string): string {
    if (keyword.includes('中药')) return 'chinese-medicine'
    if (keyword.includes('口服液')) return 'oral-solutions'
    return 'western-medicine'
  }

  private generateTags(keyword: LongTailKeyword): string[] {
    const tags = ['G6PD缺乏症', '蚕豆病']
    
    if (keyword.keyword.includes('中药')) tags.push('中药', '禁忌')
    if (keyword.keyword.includes('口服液')) tags.push('口服液', '儿童用药')
    if (keyword.keyword.includes('婴儿')) tags.push('婴幼儿', '儿科')
    
    return [...new Set(tags)]
  }

  private determineDifficulty(keyword: LongTailKeyword): 'basic' | 'intermediate' | 'advanced' {
    if (keyword.searchVolume > 100000) return 'basic'
    if (keyword.searchVolume > 10000) return 'intermediate'
    return 'advanced'
  }

  private calculatePriority(keyword: LongTailKeyword): number {
    return Math.min(100, Math.floor(keyword.searchVolume / 1000))
  }

  private determineCompetitionLevel(difficulty: number): 'low' | 'medium' | 'high' {
    if (difficulty < 30) return 'low'
    if (difficulty < 60) return 'medium'
    return 'high'
  }

  private calculateQualityScore(content: ContentVariant, keyword: LongTailKeyword): number {
    let score = 70 // 基础分

    // 内容长度评分
    if (content.content.length > 500) score += 10
    if (content.content.length > 1000) score += 10

    // 关键词覆盖评分
    if (content.content.includes(keyword.keyword)) score += 10

    // 结构化评分
    if (content.content.includes('##')) score += 5
    if (content.content.includes('1.') || content.content.includes('-')) score += 5

    return Math.min(100, score)
  }

  private calculateSEOScore(content: string, keyword: string): number {
    let score = 60 // 基础分

    // 关键词密度
    const density = this.calculateKeywordDensity(content, keyword)
    if (density >= 1 && density <= 3) score += 20
    else if (density > 0) score += 10

    // 内容长度
    if (content.length > 300) score += 10
    if (content.length > 600) score += 10

    return Math.min(100, score)
  }

  private calculateKeywordDensity(content: string, keyword: string): number {
    const words = content.toLowerCase().split(/\s+/)
    const keywordCount = words.filter(word => word.includes(keyword.toLowerCase())).length
    return words.length > 0 ? (keywordCount / words.length) * 100 : 0
  }
}

// 导出单例实例
export const contentGenerator = ContentGenerator.getInstance()
