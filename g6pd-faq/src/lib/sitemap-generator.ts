import { FAQ } from './types'
import { urlStructureManager } from './url-structure'
import fs from 'fs'
import path from 'path'

/**
 * 动态站点地图生成器
 * 支持分层sitemap.xml生成和自动更新
 */

export interface SitemapUrl {
  loc: string
  lastmod: string
  changefreq: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority: number
  alternates?: Array<{
    hreflang: string
    href: string
  }>
}

export interface SitemapIndex {
  loc: string
  lastmod: string
}

export interface SitemapConfig {
  baseUrl: string
  outputDir: string
  maxUrlsPerSitemap: number
  defaultChangefreq: SitemapUrl['changefreq']
  defaultPriority: number
}

export class SitemapGenerator {
  private static instance: SitemapGenerator
  private config: SitemapConfig

  constructor(config?: Partial<SitemapConfig>) {
    this.config = {
      baseUrl: 'https://g6pd.site',
      outputDir: './public',
      maxUrlsPerSitemap: 50000,
      defaultChangefreq: 'monthly',
      defaultPriority: 0.5,
      ...config
    }
  }

  static getInstance(config?: Partial<SitemapConfig>): SitemapGenerator {
    if (!SitemapGenerator.instance) {
      SitemapGenerator.instance = new SitemapGenerator(config)
    }
    return SitemapGenerator.instance
  }

  /**
   * 生成完整的站点地图系统
   */
  async generateSitemaps(faqs: FAQ[]): Promise<{
    mainSitemap: string
    faqSitemaps: string[]
    categorySitemaps: string[]
    sitemapIndex: string
  }> {
    console.log(`🗺️  开始生成站点地图，共 ${faqs.length} 个FAQ`)

    const result = {
      mainSitemap: '',
      faqSitemaps: [] as string[],
      categorySitemaps: [] as string[],
      sitemapIndex: ''
    }

    // 1. 生成主站点地图（静态页面）
    result.mainSitemap = await this.generateMainSitemap()

    // 2. 生成FAQ站点地图
    result.faqSitemaps = await this.generateFAQSitemaps(faqs)

    // 3. 生成分类站点地图
    result.categorySitemaps = await this.generateCategorySitemaps(faqs)

    // 4. 生成站点地图索引
    result.sitemapIndex = await this.generateSitemapIndex([
      result.mainSitemap,
      ...result.faqSitemaps,
      ...result.categorySitemaps
    ])

    console.log(`✅ 站点地图生成完成:`)
    console.log(`  - 主站点地图: ${result.mainSitemap}`)
    console.log(`  - FAQ站点地图: ${result.faqSitemaps.length} 个`)
    console.log(`  - 分类站点地图: ${result.categorySitemaps.length} 个`)
    console.log(`  - 站点地图索引: ${result.sitemapIndex}`)

    return result
  }

  /**
   * 生成主站点地图（静态页面）
   */
  private async generateMainSitemap(): Promise<string> {
    const urls: SitemapUrl[] = []

    // 主页
    urls.push({
      loc: `${this.config.baseUrl}/zh`,
      lastmod: new Date().toISOString(),
      changefreq: 'daily',
      priority: 1.0,
      alternates: [
        { hreflang: 'zh', href: `${this.config.baseUrl}/zh` },
        { hreflang: 'en', href: `${this.config.baseUrl}/en` }
      ]
    })

    urls.push({
      loc: `${this.config.baseUrl}/en`,
      lastmod: new Date().toISOString(),
      changefreq: 'daily',
      priority: 1.0,
      alternates: [
        { hreflang: 'zh', href: `${this.config.baseUrl}/zh` },
        { hreflang: 'en', href: `${this.config.baseUrl}/en` }
      ]
    })

    // FAQ首页
    urls.push({
      loc: `${this.config.baseUrl}/zh/faq`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: 0.9,
      alternates: [
        { hreflang: 'zh', href: `${this.config.baseUrl}/zh/faq` },
        { hreflang: 'en', href: `${this.config.baseUrl}/en/faq` }
      ]
    })

    urls.push({
      loc: `${this.config.baseUrl}/en/faq`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: 0.9,
      alternates: [
        { hreflang: 'zh', href: `${this.config.baseUrl}/zh/faq` },
        { hreflang: 'en', href: `${this.config.baseUrl}/en/faq` }
      ]
    })

    // 静态页面
    const staticPages = [
      { path: '/about', priority: 0.7 },
      { path: '/contact', priority: 0.6 },
      { path: '/privacy', priority: 0.5 },
      { path: '/terms', priority: 0.5 }
    ]

    staticPages.forEach(page => {
      urls.push({
        loc: `${this.config.baseUrl}/zh${page.path}`,
        lastmod: new Date().toISOString(),
        changefreq: 'monthly',
        priority: page.priority,
        alternates: [
          { hreflang: 'zh', href: `${this.config.baseUrl}/zh${page.path}` },
          { hreflang: 'en', href: `${this.config.baseUrl}/en${page.path}` }
        ]
      })

      urls.push({
        loc: `${this.config.baseUrl}/en${page.path}`,
        lastmod: new Date().toISOString(),
        changefreq: 'monthly',
        priority: page.priority,
        alternates: [
          { hreflang: 'zh', href: `${this.config.baseUrl}/zh${page.path}` },
          { hreflang: 'en', href: `${this.config.baseUrl}/en${page.path}` }
        ]
      })
    })

    const filename = 'sitemap-main.xml'
    const content = this.generateSitemapXML(urls)
    await this.writeSitemapFile(filename, content)

    return filename
  }

  /**
   * 生成FAQ站点地图
   */
  private async generateFAQSitemaps(faqs: FAQ[]): Promise<string[]> {
    const sitemapFiles: string[] = []
    const faqUrls = urlStructureManager.generateSitemapUrls(faqs)

    // 按语言分组
    const zhUrls = faqUrls.filter(url => url.url.includes('/zh/'))
    const enUrls = faqUrls.filter(url => url.url.includes('/en/'))

    // 生成中文FAQ站点地图
    const zhChunks = this.chunkArray(zhUrls, this.config.maxUrlsPerSitemap)
    for (let i = 0; i < zhChunks.length; i++) {
      const filename = `sitemap-faq-zh-${i + 1}.xml`
      const urls: SitemapUrl[] = zhChunks[i].map(url => ({
        loc: url.url,
        lastmod: url.lastmod,
        changefreq: url.changefreq as SitemapUrl['changefreq'],
        priority: url.priority,
        alternates: [
          { hreflang: 'zh', href: url.url },
          { hreflang: 'en', href: url.url.replace('/zh/', '/en/') }
        ]
      }))

      const content = this.generateSitemapXML(urls)
      await this.writeSitemapFile(filename, content)
      sitemapFiles.push(filename)
    }

    // 生成英文FAQ站点地图
    const enChunks = this.chunkArray(enUrls, this.config.maxUrlsPerSitemap)
    for (let i = 0; i < enChunks.length; i++) {
      const filename = `sitemap-faq-en-${i + 1}.xml`
      const urls: SitemapUrl[] = enChunks[i].map(url => ({
        loc: url.url,
        lastmod: url.lastmod,
        changefreq: url.changefreq as SitemapUrl['changefreq'],
        priority: url.priority,
        alternates: [
          { hreflang: 'zh', href: url.url.replace('/en/', '/zh/') },
          { hreflang: 'en', href: url.url }
        ]
      }))

      const content = this.generateSitemapXML(urls)
      await this.writeSitemapFile(filename, content)
      sitemapFiles.push(filename)
    }

    return sitemapFiles
  }

  /**
   * 生成分类站点地图
   */
  private async generateCategorySitemaps(faqs: FAQ[]): Promise<string[]> {
    const sitemapFiles: string[] = []
    const categories = this.groupFAQsByCategory(faqs)

    console.log(`📂 生成分类站点地图，共 ${Object.keys(categories).length} 个分类`)

    for (const [category, categoryFaqs] of Object.entries(categories)) {
      console.log(`  处理分类: ${category} (${categoryFaqs?.length || 0} 个FAQ)`)
      const urls: SitemapUrl[] = []

      // 分类页面URL
      const locales = ['zh', 'en']
      if (!locales) {
        console.error('❌ locales数组为undefined')
        continue
      }

      locales.forEach(locale => {
        const categoryPath = this.getCategoryPath(category)
        urls.push({
          loc: `${this.config.baseUrl}/${locale}/faq${categoryPath}`,
          lastmod: new Date().toISOString(),
          changefreq: 'weekly',
          priority: 0.8,
          alternates: [
            { hreflang: 'zh', href: `${this.config.baseUrl}/zh/faq${categoryPath}` },
            { hreflang: 'en', href: `${this.config.baseUrl}/en/faq${categoryPath}` }
          ]
        })
      })

      // 子分类页面URL
      const subcategories = this.getSubcategories(categoryFaqs) || []
      subcategories.forEach(subcategory => {
        ['zh', 'en'].forEach(locale => {
          const categoryPath = this.getCategoryPath(category)
          urls.push({
            loc: `${this.config.baseUrl}/${locale}/faq${categoryPath}/${subcategory}`,
            lastmod: new Date().toISOString(),
            changefreq: 'weekly',
            priority: 0.7,
            alternates: [
              { hreflang: 'zh', href: `${this.config.baseUrl}/zh/faq${categoryPath}/${subcategory}` },
              { hreflang: 'en', href: `${this.config.baseUrl}/en/faq${categoryPath}/${subcategory}` }
            ]
          })
        })
      })

      const filename = `sitemap-category-${category}.xml`
      const content = this.generateSitemapXML(urls)
      await this.writeSitemapFile(filename, content)
      sitemapFiles.push(filename)
    }

    return sitemapFiles
  }

  /**
   * 生成站点地图索引
   */
  private async generateSitemapIndex(sitemapFiles: string[]): Promise<string> {
    const sitemaps: SitemapIndex[] = sitemapFiles.map(filename => ({
      loc: `${this.config.baseUrl}/${filename}`,
      lastmod: new Date().toISOString()
    }))

    const content = this.generateSitemapIndexXML(sitemaps)
    const filename = 'sitemap.xml'
    await this.writeSitemapFile(filename, content)

    return filename
  }

  /**
   * 生成站点地图XML内容
   */
  private generateSitemapXML(urls: SitemapUrl[]): string {
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
    xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">\n'

    urls.forEach(url => {
      xml += '  <url>\n'
      xml += `    <loc>${this.escapeXml(url.loc)}</loc>\n`
      xml += `    <lastmod>${url.lastmod}</lastmod>\n`
      xml += `    <changefreq>${url.changefreq}</changefreq>\n`
      xml += `    <priority>${url.priority}</priority>\n`

      if (url.alternates) {
        url.alternates.forEach(alternate => {
          xml += `    <xhtml:link rel="alternate" hreflang="${alternate.hreflang}" href="${this.escapeXml(alternate.href)}" />\n`
        })
      }

      xml += '  </url>\n'
    })

    xml += '</urlset>'
    return xml
  }

  /**
   * 生成站点地图索引XML内容
   */
  private generateSitemapIndexXML(sitemaps: SitemapIndex[]): string {
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
    xml += '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n'

    sitemaps.forEach(sitemap => {
      xml += '  <sitemap>\n'
      xml += `    <loc>${this.escapeXml(sitemap.loc)}</loc>\n`
      xml += `    <lastmod>${sitemap.lastmod}</lastmod>\n`
      xml += '  </sitemap>\n'
    })

    xml += '</sitemapindex>'
    return xml
  }

  /**
   * 写入站点地图文件
   */
  private async writeSitemapFile(filename: string, content: string): Promise<void> {
    const outputPath = path.join(this.config.outputDir, filename)
    
    // 确保输出目录存在
    const outputDir = path.dirname(outputPath)
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }

    fs.writeFileSync(outputPath, content, 'utf8')
  }

  /**
   * 按分类分组FAQ
   */
  private groupFAQsByCategory(faqs: FAQ[]): Record<string, FAQ[]> {
    if (!faqs || !Array.isArray(faqs)) {
      console.warn('⚠️  FAQs数组为空或无效')
      return {}
    }

    const groups = faqs.reduce((groups, faq) => {
      if (!faq) {
        console.warn('⚠️  发现空的FAQ对象')
        return groups
      }

      const category = faq.category || 'general'
      if (!groups[category]) {
        groups[category] = []
      }
      groups[category].push(faq)
      return groups
    }, {} as Record<string, FAQ[]>)

    console.log(`📊 分类统计: ${Object.keys(groups).map(cat => `${cat}(${groups[cat].length})`).join(', ')}`)
    return groups
  }

  /**
   * 获取子分类列表
   */
  private getSubcategories(faqs: FAQ[]): string[] {
    if (!faqs || !Array.isArray(faqs)) {
      return []
    }

    const subcategories = new Set<string>()
    faqs.forEach(faq => {
      if (faq && faq.subcategory) {
        subcategories.add(faq.subcategory)
      }
    })
    return Array.from(subcategories)
  }

  /**
   * 获取分类路径
   */
  private getCategoryPath(category: string): string {
    const categoryPaths: Record<string, string> = {
      'medications': '/medications',
      'symptoms': '/symptoms',
      'diet': '/diet',
      'lifestyle': '/lifestyle',
      'emergency': '/emergency',
      'pediatric': '/pediatric'
    }
    
    return categoryPaths[category] || '/general'
  }

  /**
   * 数组分块
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize))
    }
    return chunks
  }

  /**
   * XML转义
   */
  private escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;')
  }

  /**
   * 生成robots.txt
   */
  async generateRobotsTxt(): Promise<string> {
    const content = `User-agent: *
Allow: /

# Sitemaps
Sitemap: ${this.config.baseUrl}/sitemap.xml

# Crawl-delay for respectful crawling
Crawl-delay: 1

# Disallow admin and private areas
Disallow: /admin/
Disallow: /api/
Disallow: /_next/
Disallow: /static/

# Allow important directories
Allow: /zh/
Allow: /en/
Allow: /faq/
Allow: /images/
Allow: /css/
Allow: /js/
`

    const filename = 'robots.txt'
    await this.writeSitemapFile(filename, content)
    return filename
  }
}

// 导出单例实例
export const sitemapGenerator = SitemapGenerator.getInstance()
