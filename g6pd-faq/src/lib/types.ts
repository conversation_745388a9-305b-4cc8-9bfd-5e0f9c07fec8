// 程序化SEO增强的FAQ接口
export interface FAQ {
  id: string;
  slug: string;
  title: string;
  question: string;
  answer: string;
  shortAnswer?: string;
  category: string;
  subcategory?: string;
  tags: string[];
  difficulty: 'basic' | 'intermediate' | 'advanced';
  priority: number;
  relatedFaqs: string[];
  lastUpdated: string;
  author?: string;
  medicalReview?: boolean;
  sources?: string[];
  locale: 'zh' | 'en';

  // 程序化SEO增强字段
  seo: SEOMetadata;
  longTailKeywords: LongTailKeyword[];
  searchVolume?: number;
  competitionLevel?: 'low' | 'medium' | 'high';
  contentVariants?: ContentVariant[];
  autoGenerated?: boolean;
  generationSource?: string; // 来源数据标识
  qualityScore?: number; // 内容质量评分 0-100
}

export interface Category {
  id: string;
  slug: string;
  name: string;
  description: string;
  icon?: string;
  parentId?: string;
  children?: Category[];
  faqCount: number;
  priority: number;
  locale: 'zh' | 'en';
}

export interface Medication {
  id: string;
  name: string;
  genericName?: string;
  brandNames: string[];
  category: 'prohibited' | 'caution' | 'safe';
  type: 'chinese-medicine' | 'oral-solution' | 'western-medicine';
  description: string;
  contraindications?: string[];
  alternatives?: string[];
  dosageNotes?: string;
  relatedFaqs: string[];
  locale: 'zh' | 'en';
}

export interface SearchResult {
  id: string;
  title: string;
  excerpt: string;
  url: string;
  category: string;
  type: 'faq' | 'medication' | 'category';
  relevanceScore: number;
}

export interface SiteConfig {
  name: string;
  description: string;
  url: string;
  ogImage: string;
  links: {
    github?: string;
    twitter?: string;
  };
}

export interface NavigationItem {
  title: string;
  href: string;
  description?: string;
  items?: NavigationItem[];
}

export interface BreadcrumbItem {
  title: string;
  href: string;
}

// 程序化SEO核心类型定义
export interface LongTailKeyword {
  keyword: string;
  searchVolume: number;
  difficulty: number;
  intent: 'informational' | 'navigational' | 'transactional' | 'commercial';
  variations: string[];
  relatedTerms: string[];
  source: string; // 来源文件或数据源
}

export interface ContentVariant {
  id: string;
  type: 'angle' | 'depth' | 'audience' | 'format';
  title: string;
  description: string;
  targetKeywords: string[];
  content: string;
  seoScore: number;
}

export interface SEOMetadata {
  title: string;
  description: string;
  keywords: string[];
  canonical?: string;
  ogImage?: string;
  noindex?: boolean;
  structuredData?: Record<string, unknown>;
  focusKeyword: string;
  semanticKeywords: string[];
  readabilityScore?: number;
  keywordDensity?: number;
  contentLength?: number;
}

export interface SEOData {
  title: string;
  description: string;
  keywords?: string;
  canonical?: string;
  ogImage?: string;
  noindex?: boolean;
  structuredData?: Record<string, unknown>;
}

export interface EmergencyInfo {
  title: string;
  symptoms: string[];
  actions: string[];
  whenToSeekHelp: string;
  emergencyContacts?: string[];
}

export interface DrugInteraction {
  drugName: string;
  severity: 'high' | 'medium' | 'low';
  description: string;
  alternatives?: string[];
}

export interface FaqFilter {
  category?: string;
  subcategory?: string;
  difficulty?: string;
  tags?: string[];
  search?: string;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 程序化SEO内容生成相关类型
export interface ContentTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  template: string; // 模板内容，支持变量替换
  variables: TemplateVariable[];
  seoTemplate: SEOTemplate;
  locale: 'zh' | 'en';
}

export interface TemplateVariable {
  name: string;
  type: 'string' | 'array' | 'object';
  description: string;
  required: boolean;
  defaultValue?: any;
  validation?: string; // 正则表达式验证
}

export interface SEOTemplate {
  titleTemplate: string;
  descriptionTemplate: string;
  keywordsTemplate: string[];
  structuredDataTemplate?: Record<string, unknown>;
}

// 长尾关键词数据源
export interface KeywordDataSource {
  id: string;
  name: string;
  description: string;
  filePath: string;
  format: 'csv' | 'md' | 'json';
  lastUpdated: string;
  totalKeywords: number;
  processedKeywords: number;
  status: 'pending' | 'processing' | 'completed' | 'error';
}

// 内容生成任务
export interface ContentGenerationTask {
  id: string;
  name: string;
  description: string;
  templateId: string;
  dataSourceId: string;
  targetKeywords: string[];
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number; // 0-100
  createdAt: string;
  completedAt?: string;
  generatedContent: GeneratedContent[];
  errors?: string[];
}

export interface GeneratedContent {
  id: string;
  taskId: string;
  keyword: string;
  faq: FAQ;
  qualityScore: number;
  seoScore: number;
  approved: boolean;
  publishedAt?: string;
}
