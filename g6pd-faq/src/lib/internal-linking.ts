import { FAQ } from './types'
import { urlStructureManager } from './url-structure'

/**
 * 内部链接管理器
 * 负责生成智能的内部链接策略，提升SEO效果
 */

export interface InternalLink {
  text: string
  url: string
  title: string
  rel?: string
  target?: string
}

export interface LinkingStrategy {
  contextualLinks: InternalLink[]
  relatedFAQs: InternalLink[]
  categoryLinks: InternalLink[]
  tagLinks: InternalLink[]
}

export class InternalLinkingManager {
  private static instance: InternalLinkingManager
  private faqIndex: Map<string, FAQ> = new Map()
  private keywordIndex: Map<string, FAQ[]> = new Map()
  private categoryIndex: Map<string, FAQ[]> = new Map()
  private tagIndex: Map<string, FAQ[]> = new Map()

  static getInstance(): InternalLinkingManager {
    if (!InternalLinkingManager.instance) {
      InternalLinkingManager.instance = new InternalLinkingManager()
    }
    return InternalLinkingManager.instance
  }

  /**
   * 初始化索引
   */
  initializeIndex(faqs: FAQ[]): void {
    console.log(`初始化内部链接索引，共 ${faqs.length} 个FAQ`)
    
    // 清空现有索引
    this.faqIndex.clear()
    this.keywordIndex.clear()
    this.categoryIndex.clear()
    this.tagIndex.clear()

    faqs.forEach(faq => {
      // FAQ索引
      this.faqIndex.set(faq.id, faq)

      // 关键词索引
      if (faq.longTailKeywords) {
        faq.longTailKeywords.forEach(keyword => {
          const key = keyword.keyword.toLowerCase()
          if (!this.keywordIndex.has(key)) {
            this.keywordIndex.set(key, [])
          }
          this.keywordIndex.get(key)!.push(faq)
        })
      }

      // 分类索引
      const categoryKey = `${faq.category}${faq.subcategory ? `-${faq.subcategory}` : ''}`
      if (!this.categoryIndex.has(categoryKey)) {
        this.categoryIndex.set(categoryKey, [])
      }
      this.categoryIndex.get(categoryKey)!.push(faq)

      // 标签索引
      if (faq.tags) {
        faq.tags.forEach(tag => {
          const tagKey = tag.toLowerCase()
          if (!this.tagIndex.has(tagKey)) {
            this.tagIndex.set(tagKey, [])
          }
          this.tagIndex.get(tagKey)!.push(faq)
        })
      }
    })

    console.log(`索引初始化完成:`)
    console.log(`- FAQ索引: ${this.faqIndex.size} 条`)
    console.log(`- 关键词索引: ${this.keywordIndex.size} 个关键词`)
    console.log(`- 分类索引: ${this.categoryIndex.size} 个分类`)
    console.log(`- 标签索引: ${this.tagIndex.size} 个标签`)
  }

  /**
   * 生成FAQ的链接策略
   */
  generateLinkingStrategy(faq: FAQ, locale: string = 'zh'): LinkingStrategy {
    return {
      contextualLinks: this.generateContextualLinks(faq, locale),
      relatedFAQs: this.generateRelatedFAQLinks(faq, locale),
      categoryLinks: this.generateCategoryLinks(faq, locale),
      tagLinks: this.generateTagLinks(faq, locale)
    }
  }

  /**
   * 生成上下文相关链接
   */
  private generateContextualLinks(faq: FAQ, locale: string): InternalLink[] {
    const links: InternalLink[] = []
    const content = `${faq.question} ${faq.answer}`.toLowerCase()

    // 基于内容中的关键词查找相关FAQ
    this.keywordIndex.forEach((relatedFaqs, keyword) => {
      if (content.includes(keyword) && links.length < 5) {
        const relevantFaqs = relatedFaqs
          .filter(relatedFaq => relatedFaq.id !== faq.id)
          .sort((a, b) => (b.searchVolume || 0) - (a.searchVolume || 0))
          .slice(0, 2)

        relevantFaqs.forEach(relatedFaq => {
          const urlStructure = urlStructureManager.generateFAQURLStructure(relatedFaq, locale)
          links.push({
            text: relatedFaq.title,
            url: urlStructure.path,
            title: `了解更多关于${relatedFaq.title}的信息`,
            rel: 'related'
          })
        })
      }
    })

    return this.deduplicateLinks(links).slice(0, 5)
  }

  /**
   * 生成相关FAQ链接
   */
  private generateRelatedFAQLinks(faq: FAQ, locale: string): InternalLink[] {
    const links: InternalLink[] = []
    
    // 1. 同分类的FAQ
    const categoryKey = `${faq.category}${faq.subcategory ? `-${faq.subcategory}` : ''}`
    const categoryFaqs = this.categoryIndex.get(categoryKey) || []
    
    const sameCategoryFaqs = categoryFaqs
      .filter(relatedFaq => relatedFaq.id !== faq.id)
      .sort((a, b) => (b.searchVolume || 0) - (a.searchVolume || 0))
      .slice(0, 3)

    sameCategoryFaqs.forEach(relatedFaq => {
      const urlStructure = urlStructureManager.generateFAQURLStructure(relatedFaq, locale)
      links.push({
        text: relatedFaq.title,
        url: urlStructure.path,
        title: relatedFaq.shortAnswer || relatedFaq.title
      })
    })

    // 2. 相似标签的FAQ
    if (faq.tags && links.length < 5) {
      const tagMatches = new Map<string, number>()
      
      faq.tags.forEach(tag => {
        const tagFaqs = this.tagIndex.get(tag.toLowerCase()) || []
        tagFaqs.forEach(tagFaq => {
          if (tagFaq.id !== faq.id) {
            tagMatches.set(tagFaq.id, (tagMatches.get(tagFaq.id) || 0) + 1)
          }
        })
      })

      const sortedMatches = Array.from(tagMatches.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5 - links.length)

      sortedMatches.forEach(([faqId]) => {
        const relatedFaq = this.faqIndex.get(faqId)
        if (relatedFaq) {
          const urlStructure = urlStructureManager.generateFAQURLStructure(relatedFaq, locale)
          links.push({
            text: relatedFaq.title,
            url: urlStructure.path,
            title: relatedFaq.shortAnswer || relatedFaq.title
          })
        }
      })
    }

    return this.deduplicateLinks(links).slice(0, 5)
  }

  /**
   * 生成分类链接
   */
  private generateCategoryLinks(faq: FAQ, locale: string): InternalLink[] {
    const links: InternalLink[] = []
    const isZh = locale === 'zh'

    // 主分类链接
    const categoryPath = this.getCategoryPath(faq.category)
    const categoryName = this.getCategoryName(faq.category, isZh)
    
    links.push({
      text: categoryName,
      url: `/${locale}/faq${categoryPath}`,
      title: `浏览所有${categoryName}相关问题`
    })

    // 子分类链接（如果存在）
    if (faq.subcategory) {
      const subcategoryName = this.getSubcategoryName(faq.subcategory, isZh)
      links.push({
        text: subcategoryName,
        url: `/${locale}/faq${categoryPath}/${faq.subcategory}`,
        title: `浏览所有${subcategoryName}相关问题`
      })
    }

    // 相关分类链接
    const relatedCategories = this.getRelatedCategories(faq.category)
    relatedCategories.forEach(category => {
      const relatedCategoryPath = this.getCategoryPath(category)
      const relatedCategoryName = this.getCategoryName(category, isZh)
      
      links.push({
        text: relatedCategoryName,
        url: `/${locale}/faq${relatedCategoryPath}`,
        title: `浏览${relatedCategoryName}相关问题`
      })
    })

    return links.slice(0, 4)
  }

  /**
   * 生成标签链接
   */
  private generateTagLinks(faq: FAQ, locale: string): InternalLink[] {
    const links: InternalLink[] = []

    if (faq.tags) {
      faq.tags.slice(0, 5).forEach(tag => {
        links.push({
          text: `#${tag}`,
          url: `/${locale}/faq/tags/${encodeURIComponent(tag.toLowerCase())}`,
          title: `查看所有${tag}相关问题`
        })
      })
    }

    return links
  }

  /**
   * 去重链接
   */
  private deduplicateLinks(links: InternalLink[]): InternalLink[] {
    const seen = new Set<string>()
    return links.filter(link => {
      const key = link.url
      if (seen.has(key)) {
        return false
      }
      seen.add(key)
      return true
    })
  }

  /**
   * 获取分类路径
   */
  private getCategoryPath(category: string): string {
    const categoryPaths: Record<string, string> = {
      'medications': '/medications',
      'symptoms': '/symptoms',
      'diet': '/diet',
      'lifestyle': '/lifestyle',
      'emergency': '/emergency',
      'pediatric': '/pediatric'
    }
    
    return categoryPaths[category] || '/general'
  }

  /**
   * 获取分类名称
   */
  private getCategoryName(category: string, isZh: boolean): string {
    const categoryNames: Record<string, { zh: string; en: string }> = {
      'medications': { zh: '药物相关', en: 'Medications' },
      'symptoms': { zh: '症状表现', en: 'Symptoms' },
      'diet': { zh: '饮食指南', en: 'Diet' },
      'lifestyle': { zh: '生活方式', en: 'Lifestyle' },
      'emergency': { zh: '紧急情况', en: 'Emergency' },
      'pediatric': { zh: '儿童相关', en: 'Pediatric' }
    }
    
    return categoryNames[category]?.[isZh ? 'zh' : 'en'] || (isZh ? '其他' : 'Other')
  }

  /**
   * 获取子分类名称
   */
  private getSubcategoryName(subcategory: string, isZh: boolean): string {
    const subcategoryNames: Record<string, { zh: string; en: string }> = {
      'chinese-medicine': { zh: '中药', en: 'Chinese Medicine' },
      'western-medicine': { zh: '西药', en: 'Western Medicine' },
      'oral-solutions': { zh: '口服液', en: 'Oral Solutions' },
      'antibiotics': { zh: '抗生素', en: 'Antibiotics' }
    }
    
    return subcategoryNames[subcategory]?.[isZh ? 'zh' : 'en'] || subcategory
  }

  /**
   * 获取相关分类
   */
  private getRelatedCategories(category: string): string[] {
    const relatedMap: Record<string, string[]> = {
      'medications': ['symptoms', 'emergency'],
      'symptoms': ['medications', 'emergency', 'pediatric'],
      'diet': ['lifestyle', 'pediatric'],
      'lifestyle': ['diet'],
      'emergency': ['medications', 'symptoms'],
      'pediatric': ['medications', 'symptoms', 'diet']
    }
    
    return relatedMap[category] || []
  }

  /**
   * 在内容中插入内部链接
   */
  insertLinksInContent(content: string, faq: FAQ, locale: string = 'zh'): string {
    let processedContent = content
    const strategy = this.generateLinkingStrategy(faq, locale)
    
    // 插入上下文链接
    strategy.contextualLinks.forEach(link => {
      const linkText = link.text
      const linkHtml = `<a href="${link.url}" title="${link.title}" rel="${link.rel || ''}">${linkText}</a>`
      
      // 只替换第一次出现的文本
      const regex = new RegExp(`\\b${linkText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i')
      if (regex.test(processedContent)) {
        processedContent = processedContent.replace(regex, linkHtml)
      }
    })
    
    return processedContent
  }

  /**
   * 生成链接密度报告
   */
  generateLinkDensityReport(content: string): {
    totalWords: number
    totalLinks: number
    linkDensity: number
    recommendation: string
  } {
    const words = content.split(/\s+/).filter(word => word.trim().length > 0)
    const linkMatches = content.match(/<a[^>]*>.*?<\/a>/gi) || []
    
    const totalWords = words.length
    const totalLinks = linkMatches.length
    const linkDensity = totalWords > 0 ? (totalLinks / totalWords) * 100 : 0
    
    let recommendation = ''
    if (linkDensity < 1) {
      recommendation = '链接密度较低，建议增加相关内部链接'
    } else if (linkDensity > 5) {
      recommendation = '链接密度过高，建议减少链接数量'
    } else {
      recommendation = '链接密度适中'
    }
    
    return {
      totalWords,
      totalLinks,
      linkDensity: Math.round(linkDensity * 100) / 100,
      recommendation
    }
  }
}

// 导出单例实例
export const internalLinkingManager = InternalLinkingManager.getInstance()
