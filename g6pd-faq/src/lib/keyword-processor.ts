import { LongTailKeyword, KeywordDataSource } from './types'
import fs from 'fs'
import path from 'path'

/**
 * 长尾关键词数据处理器
 * 负责解析、清洗、分类和结构化存储长尾关键词数据
 */
export class KeywordProcessor {
  private static instance: KeywordProcessor
  private processedKeywords: Map<string, LongTailKeyword[]> = new Map()
  private keywordCategories: Map<string, string[]> = new Map()

  static getInstance(): KeywordProcessor {
    if (!KeywordProcessor.instance) {
      KeywordProcessor.instance = new KeywordProcessor()
    }
    return KeywordProcessor.instance
  }

  /**
   * 处理长尾关键词文件
   */
  async processKeywordFile(filePath: string, sourceId: string): Promise<LongTailKeyword[]> {
    console.log(`开始处理关键词文件: ${filePath}`)
    
    const fullPath = path.join(process.cwd(), filePath)
    if (!fs.existsSync(fullPath)) {
      throw new Error(`文件不存在: ${filePath}`)
    }

    const content = fs.readFileSync(fullPath, 'utf8')
    const rawKeywords = this.parseKeywordFile(content, filePath)
    
    console.log(`解析到 ${rawKeywords.length} 个原始关键词`)
    
    // 数据清洗
    const cleanedKeywords = this.cleanKeywords(rawKeywords)
    console.log(`清洗后剩余 ${cleanedKeywords.length} 个关键词`)
    
    // 分类处理
    const categorizedKeywords = this.categorizeKeywords(cleanedKeywords, sourceId)
    console.log(`分类完成，共 ${categorizedKeywords.length} 个关键词`)
    
    // 缓存处理结果
    this.processedKeywords.set(sourceId, categorizedKeywords)
    
    // 保存处理结果
    await this.saveProcessedKeywords(sourceId, categorizedKeywords)
    
    return categorizedKeywords
  }

  /**
   * 解析关键词文件内容
   */
  private parseKeywordFile(content: string, filePath: string): Partial<LongTailKeyword>[] {
    const lines = content.split('\n').filter(line => line.trim())
    const keywords: Partial<LongTailKeyword>[] = []

    // 跳过标题行
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim()
      if (!line) continue

      const parts = line.split('\t')
      if (parts.length >= 3) {
        const keyword = parts[0].trim()
        const searchVolume = this.parseSearchVolume(parts[2])
        
        keywords.push({
          keyword,
          searchVolume,
          source: filePath
        })
      }
    }

    return keywords
  }

  /**
   * 解析搜索量数据
   */
  private parseSearchVolume(volumeStr: string): number {
    // 处理各种格式的搜索量数据
    const cleaned = volumeStr.replace(/[,，]/g, '').trim()
    
    if (cleaned === '0' || cleaned === '未收录' || cleaned === '') {
      return 0
    }
    
    const num = parseInt(cleaned)
    return isNaN(num) ? 0 : num
  }

  /**
   * 数据清洗
   */
  private cleanKeywords(rawKeywords: Partial<LongTailKeyword>[]): Partial<LongTailKeyword>[] {
    return rawKeywords
      .filter(kw => kw.keyword && kw.keyword.trim().length > 0)
      .filter(kw => kw.searchVolume && kw.searchVolume > 0)
      .filter(kw => this.isValidKeyword(kw.keyword!))
      .map(kw => ({
        ...kw,
        keyword: this.normalizeKeyword(kw.keyword!)
      }))
      .filter((kw, index, arr) => 
        // 去重：保留第一个出现的关键词
        arr.findIndex(item => item.keyword === kw.keyword) === index
      )
  }

  /**
   * 验证关键词有效性
   */
  private isValidKeyword(keyword: string): boolean {
    // 过滤掉无效的关键词
    const invalidPatterns = [
      /^[\d\s]*$/, // 纯数字或空格
      /^[^\u4e00-\u9fa5a-zA-Z]*$/, // 不包含中文或英文字母
      /(.)\1{3,}/, // 连续重复字符
    ]

    return !invalidPatterns.some(pattern => pattern.test(keyword))
  }

  /**
   * 标准化关键词
   */
  private normalizeKeyword(keyword: string): string {
    return keyword
      .trim()
      .replace(/\s+/g, ' ') // 多个空格合并为一个
      .replace(/[""'']/g, '"') // 统一引号
      .replace(/[，]/g, ',') // 统一逗号
  }

  /**
   * 关键词分类
   */
  private categorizeKeywords(keywords: Partial<LongTailKeyword>[], sourceId: string): LongTailKeyword[] {
    return keywords.map(kw => {
      const fullKeyword: LongTailKeyword = {
        keyword: kw.keyword!,
        searchVolume: kw.searchVolume!,
        difficulty: this.calculateDifficulty(kw.searchVolume!),
        intent: this.determineIntent(kw.keyword!),
        variations: this.generateVariations(kw.keyword!),
        relatedTerms: this.extractRelatedTerms(kw.keyword!),
        source: kw.source!
      }

      return fullKeyword
    })
  }

  /**
   * 计算关键词难度
   */
  private calculateDifficulty(searchVolume: number): number {
    if (searchVolume > 500000) return 90
    if (searchVolume > 100000) return 80
    if (searchVolume > 50000) return 70
    if (searchVolume > 10000) return 60
    if (searchVolume > 1000) return 40
    return 20
  }

  /**
   * 判断搜索意图
   */
  private determineIntent(keyword: string): 'informational' | 'navigational' | 'transactional' | 'commercial' {
    const patterns = {
      informational: ['什么', '如何', '怎么', '为什么', '哪些', '能不能', '可以', '是否', '有什么', '怎样'],
      transactional: ['购买', '价格', '多少钱', '哪里买', '订购', '预约'],
      commercial: ['最好', '推荐', '比较', '评价', '排行', '品牌'],
      navigational: ['官网', '网站', '地址', '电话', '联系']
    }

    for (const [intent, words] of Object.entries(patterns)) {
      if (words.some(word => keyword.includes(word))) {
        return intent as any
      }
    }

    return 'informational' // 默认为信息型
  }

  /**
   * 生成关键词变体
   */
  private generateVariations(keyword: string): string[] {
    const variations: string[] = []
    
    // 同义词替换
    const synonyms = [
      ['蚕豆病', 'G6PD缺乏症'],
      ['不能吃', '禁用', '不可以吃', '不能用'],
      ['可以吃', '能吃', '能用', '可以用'],
      ['哪些', '什么'],
      ['婴儿', '宝宝', '小儿', '儿童'],
      ['中药', '草药', '中成药'],
      ['口服液', '糖浆', '液体药物']
    ]

    synonyms.forEach(([original, ...alternatives]) => {
      if (keyword.includes(original)) {
        alternatives.forEach(alt => {
          const variation = keyword.replace(original, alt)
          if (variation !== keyword && !variations.includes(variation)) {
            variations.push(variation)
          }
        })
      }
    })

    // 语序调整
    if (keyword.includes('蚕豆病') && keyword.includes('不能吃')) {
      const reordered = keyword.replace('蚕豆病', '').replace('不能吃', '') + '蚕豆病不能吃'
      if (!variations.includes(reordered)) {
        variations.push(reordered)
      }
    }

    return variations.slice(0, 5) // 限制变体数量
  }

  /**
   * 提取相关术语
   */
  private extractRelatedTerms(keyword: string): string[] {
    const relatedTerms: string[] = []
    
    // 医学术语
    const medicalTerms = ['溶血', '贫血', '黄疸', '血红蛋白', '红细胞', '葡萄糖-6-磷酸脱氢酶']
    
    // 药物术语
    const medicineTerms = ['药物', '成分', '禁忌', '副作用', '不良反应', '用药安全']
    
    // 症状术语
    const symptomTerms = ['症状', '表现', '反应', '发作', '急性期']

    if (keyword.includes('中药')) {
      relatedTerms.push(...medicalTerms, '中医', '草药', '方剂', '配伍')
    }
    
    if (keyword.includes('口服液')) {
      relatedTerms.push(...medicalTerms, '液体药物', '儿童用药', '剂量')
    }

    if (keyword.includes('婴儿') || keyword.includes('宝宝')) {
      relatedTerms.push(...symptomTerms, '儿科', '新生儿', '发育')
    }

    return [...new Set(relatedTerms)].slice(0, 8) // 去重并限制数量
  }

  /**
   * 保存处理结果
   */
  private async saveProcessedKeywords(sourceId: string, keywords: LongTailKeyword[]): Promise<void> {
    const outputDir = path.join(process.cwd(), 'src/data/processed-keywords')
    
    // 确保目录存在
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }

    const outputFile = path.join(outputDir, `${sourceId}.json`)
    
    const data = {
      sourceId,
      processedAt: new Date().toISOString(),
      totalKeywords: keywords.length,
      keywords: keywords.sort((a, b) => b.searchVolume - a.searchVolume)
    }

    fs.writeFileSync(outputFile, JSON.stringify(data, null, 2), 'utf8')
    console.log(`处理结果已保存到: ${outputFile}`)
  }

  /**
   * 获取关键词统计信息
   */
  getKeywordStats(sourceId: string): {
    total: number;
    byIntent: Record<string, number>;
    byDifficulty: Record<string, number>;
    topKeywords: LongTailKeyword[];
  } {
    const keywords = this.processedKeywords.get(sourceId) || []
    
    const byIntent: Record<string, number> = {}
    const byDifficulty: Record<string, number> = {}

    keywords.forEach(kw => {
      byIntent[kw.intent] = (byIntent[kw.intent] || 0) + 1
      
      const difficultyLevel = kw.difficulty > 70 ? 'high' : kw.difficulty > 40 ? 'medium' : 'low'
      byDifficulty[difficultyLevel] = (byDifficulty[difficultyLevel] || 0) + 1
    })

    return {
      total: keywords.length,
      byIntent,
      byDifficulty,
      topKeywords: keywords.slice(0, 10)
    }
  }

  /**
   * 按分类获取关键词
   */
  getKeywordsByCategory(sourceId: string, category: string): LongTailKeyword[] {
    const keywords = this.processedKeywords.get(sourceId) || []
    
    const categoryPatterns: Record<string, string[]> = {
      'chinese-medicine': ['中药', '草药', '中成药', '方剂'],
      'oral-solutions': ['口服液', '糖浆', '液体药物'],
      'western-medicine': ['西药', '化学药', '抗生素'],
      'pediatric': ['婴儿', '宝宝', '小儿', '儿童', '新生儿'],
      'symptoms': ['症状', '表现', '反应', '发作'],
      'safety': ['安全', '禁忌', '注意', '风险']
    }

    const patterns = categoryPatterns[category] || []
    
    return keywords.filter(kw => 
      patterns.some(pattern => kw.keyword.includes(pattern))
    )
  }

  /**
   * 搜索关键词
   */
  searchKeywords(sourceId: string, query: string, limit: number = 20): LongTailKeyword[] {
    const keywords = this.processedKeywords.get(sourceId) || []
    const normalizedQuery = query.toLowerCase().trim()
    
    return keywords
      .filter(kw => 
        kw.keyword.toLowerCase().includes(normalizedQuery) ||
        kw.variations.some(v => v.toLowerCase().includes(normalizedQuery)) ||
        kw.relatedTerms.some(t => t.toLowerCase().includes(normalizedQuery))
      )
      .sort((a, b) => b.searchVolume - a.searchVolume)
      .slice(0, limit)
  }
}

// 导出单例实例
export const keywordProcessor = KeywordProcessor.getInstance()
