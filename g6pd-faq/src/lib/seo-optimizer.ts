import { FAQ, SEOMetadata } from './types'
import { urlStructureManager } from './url-structure'
import { internalLinkingManager } from './internal-linking'

/**
 * SEO优化管理器
 * 负责生成优化的meta标签、结构化数据和关键词密度控制
 */

export interface SEOOptimizationResult {
  metaTags: MetaTag[]
  structuredData: any[]
  openGraph: OpenGraphData
  twitterCard: TwitterCardData
  canonicalUrl: string
  alternateUrls: Record<string, string>
  keywordDensity: KeywordDensityReport
  recommendations: SEORecommendation[]
}

export interface MetaTag {
  name?: string
  property?: string
  content: string
  httpEquiv?: string
}

export interface OpenGraphData {
  title: string
  description: string
  type: string
  url: string
  image?: string
  siteName: string
  locale: string
  alternateLocales: string[]
}

export interface TwitterCardData {
  card: string
  title: string
  description: string
  image?: string
  site?: string
  creator?: string
}

export interface KeywordDensityReport {
  focusKeyword: string
  density: number
  occurrences: number
  totalWords: number
  recommendations: string[]
  relatedKeywords: Array<{
    keyword: string
    density: number
    occurrences: number
  }>
}

export interface SEORecommendation {
  type: 'warning' | 'suggestion' | 'error'
  category: string
  message: string
  priority: number
}

export class SEOOptimizer {
  private static instance: SEOOptimizer
  private baseUrl: string = 'https://g6pd.site'
  private siteName: string = 'G6PD缺乏症信息中心'

  static getInstance(): SEOOptimizer {
    if (!SEOOptimizer.instance) {
      SEOOptimizer.instance = new SEOOptimizer()
    }
    return SEOOptimizer.instance
  }

  /**
   * 为FAQ页面生成完整的SEO优化
   */
  optimizeFAQPage(faq: FAQ, locale: string = 'zh'): SEOOptimizationResult {
    const urlStructure = urlStructureManager.generateFAQURLStructure(faq, locale)
    const isZh = locale === 'zh'

    return {
      metaTags: this.generateMetaTags(faq, locale),
      structuredData: this.generateStructuredData(faq, urlStructure.canonicalUrl, locale),
      openGraph: this.generateOpenGraphData(faq, urlStructure.canonicalUrl, locale),
      twitterCard: this.generateTwitterCardData(faq, locale),
      canonicalUrl: urlStructure.canonicalUrl,
      alternateUrls: urlStructure.alternateUrls,
      keywordDensity: this.analyzeKeywordDensity(faq),
      recommendations: this.generateSEORecommendations(faq, locale)
    }
  }

  /**
   * 生成meta标签
   */
  private generateMetaTags(faq: FAQ, locale: string): MetaTag[] {
    const isZh = locale === 'zh'
    const tags: MetaTag[] = []

    // 基础meta标签
    tags.push(
      { name: 'description', content: faq.seo?.description || faq.shortAnswer },
      { name: 'keywords', content: this.generateKeywordsString(faq) },
      { name: 'author', content: this.siteName },
      { name: 'robots', content: 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1' },
      { name: 'googlebot', content: 'index, follow' },
      { name: 'bingbot', content: 'index, follow' }
    )

    // 语言和地区
    tags.push(
      { name: 'language', content: locale },
      { httpEquiv: 'content-language', content: locale }
    )

    // 医疗内容相关
    tags.push(
      { name: 'medical-disclaimer', content: isZh ? '本内容仅供参考，不能替代专业医疗建议' : 'This content is for reference only and cannot replace professional medical advice' },
      { name: 'content-type', content: 'medical-information' }
    )

    // 移动优化
    tags.push(
      { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },
      { name: 'format-detection', content: 'telephone=no' }
    )

    // 缓存控制
    tags.push(
      { httpEquiv: 'cache-control', content: 'public, max-age=3600' }
    )

    return tags
  }

  /**
   * 生成结构化数据
   */
  private generateStructuredData(faq: FAQ, canonicalUrl: string, locale: string): any[] {
    const isZh = locale === 'zh'
    const structuredData: any[] = []

    // FAQ页面结构化数据
    structuredData.push({
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      'mainEntity': {
        '@type': 'Question',
        'name': faq.question,
        'acceptedAnswer': {
          '@type': 'Answer',
          'text': faq.answer,
          'author': {
            '@type': 'Organization',
            'name': this.siteName
          },
          'dateCreated': faq.lastUpdated,
          'upvoteCount': Math.floor((faq.qualityScore || 80) / 10)
        }
      },
      'url': canonicalUrl,
      'datePublished': faq.lastUpdated,
      'dateModified': faq.lastUpdated,
      'author': {
        '@type': 'Organization',
        'name': this.siteName,
        'url': this.baseUrl
      },
      'publisher': {
        '@type': 'Organization',
        'name': this.siteName,
        'url': this.baseUrl,
        'logo': {
          '@type': 'ImageObject',
          'url': `${this.baseUrl}/logo.png`
        }
      },
      'inLanguage': locale,
      'isPartOf': {
        '@type': 'WebSite',
        'name': this.siteName,
        'url': this.baseUrl
      }
    })

    // 医疗内容结构化数据
    structuredData.push({
      '@context': 'https://schema.org',
      '@type': 'MedicalWebPage',
      'name': faq.title,
      'description': faq.shortAnswer,
      'url': canonicalUrl,
      'medicalAudience': {
        '@type': 'MedicalAudience',
        'audienceType': isZh ? '患者和家属' : 'Patients and Families'
      },
      'about': {
        '@type': 'MedicalCondition',
        'name': isZh ? 'G6PD缺乏症' : 'G6PD Deficiency',
        'alternateName': isZh ? '蚕豆病' : 'Favism',
        'code': {
          '@type': 'MedicalCode',
          'code': 'D55.0',
          'codingSystem': 'ICD-10'
        }
      },
      'lastReviewed': faq.lastUpdated,
      'reviewedBy': {
        '@type': 'Organization',
        'name': this.siteName
      }
    })

    // 面包屑导航结构化数据
    const urlStructure = urlStructureManager.generateFAQURLStructure(faq, locale)
    structuredData.push({
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      'itemListElement': urlStructure.breadcrumbs.map(crumb => ({
        '@type': 'ListItem',
        'position': crumb.position,
        'name': crumb.name,
        'item': `${this.baseUrl}${crumb.url}`
      }))
    })

    return structuredData
  }

  /**
   * 生成Open Graph数据
   */
  private generateOpenGraphData(faq: FAQ, canonicalUrl: string, locale: string): OpenGraphData {
    const isZh = locale === 'zh'
    
    return {
      title: `${faq.title} - ${this.siteName}`,
      description: faq.shortAnswer,
      type: 'article',
      url: canonicalUrl,
      image: `${this.baseUrl}/og-image.png`,
      siteName: this.siteName,
      locale: locale === 'zh' ? 'zh_CN' : 'en_US',
      alternateLocales: locale === 'zh' ? ['en_US'] : ['zh_CN']
    }
  }

  /**
   * 生成Twitter Card数据
   */
  private generateTwitterCardData(faq: FAQ, locale: string): TwitterCardData {
    return {
      card: 'summary_large_image',
      title: faq.title,
      description: faq.shortAnswer,
      image: `${this.baseUrl}/twitter-card.png`,
      site: '@g6pd_info',
      creator: '@g6pd_info'
    }
  }

  /**
   * 分析关键词密度
   */
  private analyzeKeywordDensity(faq: FAQ): KeywordDensityReport {
    const content = `${faq.question} ${faq.answer}`.toLowerCase()
    const words = content.split(/\s+/).filter(word => word.trim().length > 0)
    const totalWords = words.length

    const focusKeyword = faq.seo?.focusKeyword || faq.longTailKeywords?.[0]?.keyword || ''
    const focusKeywordLower = focusKeyword.toLowerCase()
    
    // 计算主关键词密度
    const focusOccurrences = this.countKeywordOccurrences(content, focusKeywordLower)
    const focusDensity = totalWords > 0 ? (focusOccurrences / totalWords) * 100 : 0

    // 分析相关关键词
    const relatedKeywords: Array<{ keyword: string; density: number; occurrences: number }> = []
    
    if (faq.longTailKeywords) {
      faq.longTailKeywords.slice(1, 6).forEach(kwData => {
        const keyword = kwData.keyword.toLowerCase()
        const occurrences = this.countKeywordOccurrences(content, keyword)
        const density = totalWords > 0 ? (occurrences / totalWords) * 100 : 0
        
        if (occurrences > 0) {
          relatedKeywords.push({
            keyword: kwData.keyword,
            density: Math.round(density * 100) / 100,
            occurrences
          })
        }
      })
    }

    // 生成建议
    const recommendations: string[] = []
    if (focusDensity < 0.5) {
      recommendations.push('主关键词密度过低，建议增加关键词使用')
    } else if (focusDensity > 3) {
      recommendations.push('主关键词密度过高，可能被视为关键词堆砌')
    } else {
      recommendations.push('关键词密度适中')
    }

    if (relatedKeywords.length < 2) {
      recommendations.push('建议增加相关关键词以提升内容相关性')
    }

    return {
      focusKeyword,
      density: Math.round(focusDensity * 100) / 100,
      occurrences: focusOccurrences,
      totalWords,
      recommendations,
      relatedKeywords
    }
  }

  /**
   * 计算关键词出现次数
   */
  private countKeywordOccurrences(content: string, keyword: string): number {
    if (!keyword.trim()) return 0
    
    const regex = new RegExp(keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi')
    const matches = content.match(regex)
    return matches ? matches.length : 0
  }

  /**
   * 生成SEO建议
   */
  private generateSEORecommendations(faq: FAQ, locale: string): SEORecommendation[] {
    const recommendations: SEORecommendation[] = []

    // 标题长度检查
    if (faq.title.length < 30) {
      recommendations.push({
        type: 'suggestion',
        category: 'title',
        message: '标题长度较短，建议扩展到30-60个字符以提升SEO效果',
        priority: 70
      })
    } else if (faq.title.length > 60) {
      recommendations.push({
        type: 'warning',
        category: 'title',
        message: '标题过长，可能在搜索结果中被截断',
        priority: 80
      })
    }

    // 描述长度检查
    const description = faq.seo?.description || faq.shortAnswer
    if (description.length < 120) {
      recommendations.push({
        type: 'suggestion',
        category: 'description',
        message: '描述长度较短，建议扩展到120-160个字符',
        priority: 60
      })
    } else if (description.length > 160) {
      recommendations.push({
        type: 'warning',
        category: 'description',
        message: '描述过长，可能在搜索结果中被截断',
        priority: 70
      })
    }

    // 内容长度检查
    if (faq.answer.length < 300) {
      recommendations.push({
        type: 'suggestion',
        category: 'content',
        message: '内容长度较短，建议增加更多详细信息以提升权威性',
        priority: 50
      })
    }

    // 关键词检查
    if (!faq.longTailKeywords || faq.longTailKeywords.length === 0) {
      recommendations.push({
        type: 'error',
        category: 'keywords',
        message: '缺少长尾关键词，影响搜索排名',
        priority: 90
      })
    }

    // 内链检查
    const linkStrategy = internalLinkingManager.generateLinkingStrategy(faq, locale)
    if (linkStrategy.relatedFAQs.length === 0) {
      recommendations.push({
        type: 'suggestion',
        category: 'internal-links',
        message: '建议添加相关FAQ的内部链接以提升页面权重',
        priority: 40
      })
    }

    // 医疗审核检查
    if (!faq.medicalReview) {
      recommendations.push({
        type: 'warning',
        category: 'medical-review',
        message: '医疗内容建议经过专业审核',
        priority: 85
      })
    }

    // 按优先级排序
    return recommendations.sort((a, b) => b.priority - a.priority)
  }

  /**
   * 生成关键词字符串
   */
  private generateKeywordsString(faq: FAQ): string {
    const keywords: string[] = []
    
    // 添加主要关键词
    if (faq.seo?.keywords) {
      keywords.push(...faq.seo.keywords)
    }
    
    // 添加长尾关键词
    if (faq.longTailKeywords) {
      faq.longTailKeywords.slice(0, 5).forEach(kw => {
        keywords.push(kw.keyword)
        if (kw.variations) {
          keywords.push(...kw.variations.slice(0, 2))
        }
      })
    }
    
    // 添加标签
    if (faq.tags) {
      keywords.push(...faq.tags.slice(0, 3))
    }
    
    // 去重并限制数量
    const uniqueKeywords = Array.from(new Set(keywords))
    return uniqueKeywords.slice(0, 10).join(', ')
  }

  /**
   * 生成页面性能报告
   */
  generatePerformanceReport(faq: FAQ, locale: string): {
    seoScore: number
    issues: SEORecommendation[]
    strengths: string[]
    improvements: string[]
  } {
    const recommendations = this.generateSEORecommendations(faq, locale)
    const keywordDensity = this.analyzeKeywordDensity(faq)
    
    let seoScore = 100
    
    // 根据问题扣分
    recommendations.forEach(rec => {
      if (rec.type === 'error') {
        seoScore -= 20
      } else if (rec.type === 'warning') {
        seoScore -= 10
      } else if (rec.type === 'suggestion') {
        seoScore -= 5
      }
    })
    
    seoScore = Math.max(0, seoScore)
    
    const strengths: string[] = []
    const improvements: string[] = []
    
    // 分析优势
    if (faq.title.length >= 30 && faq.title.length <= 60) {
      strengths.push('标题长度适中')
    }
    
    if (keywordDensity.density >= 0.5 && keywordDensity.density <= 3) {
      strengths.push('关键词密度合理')
    }
    
    if (faq.longTailKeywords && faq.longTailKeywords.length > 0) {
      strengths.push('包含长尾关键词')
    }
    
    if (faq.answer.length >= 300) {
      strengths.push('内容详细充实')
    }
    
    // 分析改进点
    recommendations.forEach(rec => {
      if (rec.priority >= 70) {
        improvements.push(rec.message)
      }
    })
    
    return {
      seoScore,
      issues: recommendations,
      strengths,
      improvements
    }
  }
}

// 导出单例实例
export const seoOptimizer = SEOOptimizer.getInstance()
