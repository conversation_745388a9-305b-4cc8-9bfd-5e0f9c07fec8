import { FAQ, Category, Medication } from './types'
import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'

// FAQ数据管理
export class FAQManager {
  private static instance: FAQManager
  private faqs: Map<string, FAQ[]> = new Map()
  private categories: Map<string, Category[]> = new Map()
  private medications: Map<string, Medication[]> = new Map()

  static getInstance(): FAQManager {
    if (!FAQManager.instance) {
      FAQManager.instance = new FAQManager()
    }
    return FAQManager.instance
  }

  // 加载FAQ数据
  async loadFAQs(locale: string): Promise<FAQ[]> {
    if (this.faqs.has(locale)) {
      return this.faqs.get(locale)!
    }

    const faqs: FAQ[] = []

    // 1. 加载传统的Markdown FAQ文件
    const faqsDir = path.join(process.cwd(), 'src/data/faqs', locale)
    if (fs.existsSync(faqsDir)) {
      const files = fs.readdirSync(faqsDir, { recursive: true })

      for (const file of files) {
        if (typeof file === 'string' && file.endsWith('.md')) {
          const filePath = path.join(faqsDir, file)
          const fileContent = fs.readFileSync(filePath, 'utf8')
          const { data, content } = matter(fileContent)

          const faq: FAQ = {
            id: data.id || this.generateId(),
            slug: data.slug || this.generateSlug(data.title),
            title: data.title,
            question: data.question || data.title,
            answer: content,
            shortAnswer: data.shortAnswer,
            category: data.category,
            subcategory: data.subcategory,
            tags: data.tags || [],
            difficulty: data.difficulty || 'basic',
            priority: data.priority || 0,
            relatedFaqs: data.relatedFaqs || [],
            lastUpdated: data.lastUpdated || new Date().toISOString(),
            author: data.author,
            medicalReview: data.medicalReview || false,
            sources: data.sources || [],
            locale: locale as 'zh' | 'en',
            seo: data.seo || {
              title: data.title,
              description: data.shortAnswer || content.substring(0, 160),
              keywords: data.tags || []
            },
            longTailKeywords: data.longTailKeywords || []
          }

          faqs.push(faq)
        }
      }
    }

    // 2. 加载程序化SEO生成的FAQ内容
    const generatedFaqs = this.loadGeneratedFAQs(locale)
    faqs.push(...generatedFaqs)

    // 按优先级和更新时间排序
    faqs.sort((a, b) => {
      if (a.priority !== b.priority) {
        return b.priority - a.priority
      }
      return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()
    })

    this.faqs.set(locale, faqs)
    return faqs
  }

  // 加载程序化SEO生成的FAQ内容
  private loadGeneratedFAQs(locale: string): FAQ[] {
    const faqs: FAQ[] = []
    const generatedDir = path.join(process.cwd(), 'src/data/generated-faqs')

    if (!fs.existsSync(generatedDir)) {
      console.warn('Generated FAQs directory not found:', generatedDir)
      return faqs
    }

    try {
      const sources = fs.readdirSync(generatedDir)

      for (const source of sources) {
        const sourcePath = path.join(generatedDir, source)
        if (fs.statSync(sourcePath).isDirectory()) {
          const files = fs.readdirSync(sourcePath)
            .filter(file => file.endsWith('.json') && file !== 'index.json')

          for (const file of files) {
            try {
              const filePath = path.join(sourcePath, file)
              const faqData = JSON.parse(fs.readFileSync(filePath, 'utf8'))

              // 只加载匹配语言的FAQ
              if (faqData.locale === locale) {
                faqs.push(faqData)
              }
            } catch (error) {
              console.warn(`Failed to load generated FAQ file: ${file}`, error)
            }
          }
        }
      }
    } catch (error) {
      console.warn('Error loading generated FAQs:', error)
    }

    return faqs
  }

  // 获取单个FAQ
  async getFAQ(slug: string, locale: string): Promise<FAQ | null> {
    const faqs = await this.loadFAQs(locale)
    return faqs.find(faq => faq.slug === slug) || null
  }

  // 按分类获取FAQ
  async getFAQsByCategory(category: string, locale: string): Promise<FAQ[]> {
    const faqs = await this.loadFAQs(locale)
    return faqs.filter(faq => faq.category === category)
  }

  // 搜索FAQ
  async searchFAQs(query: string, locale: string): Promise<FAQ[]> {
    const faqs = await this.loadFAQs(locale)
    const normalizedQuery = query.toLowerCase()
    
    return faqs.filter(faq => 
      faq.title.toLowerCase().includes(normalizedQuery) ||
      faq.question.toLowerCase().includes(normalizedQuery) ||
      faq.answer.toLowerCase().includes(normalizedQuery) ||
      faq.tags.some(tag => tag.toLowerCase().includes(normalizedQuery))
    )
  }

  // 获取相关FAQ
  async getRelatedFAQs(faqId: string, locale: string, limit: number = 5): Promise<FAQ[]> {
    const faqs = await this.loadFAQs(locale)
    const currentFaq = faqs.find(faq => faq.id === faqId)
    
    if (!currentFaq) return []

    // 基于标签和分类的相关性计算
    const related = faqs
      .filter(faq => faq.id !== faqId)
      .map(faq => {
        let score = 0
        
        // 同分类加分
        if (faq.category === currentFaq.category) score += 3
        if (faq.subcategory === currentFaq.subcategory) score += 2
        
        // 共同标签加分
        const commonTags = faq.tags.filter(tag => currentFaq.tags.includes(tag))
        score += commonTags.length * 2
        
        return { faq, score }
      })
      .filter(item => item.score > 0)
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(item => item.faq)

    return related
  }

  // 生成ID
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9)
  }

  // 生成slug
  private generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, '')
      .replace(/\s+/g, '-')
      .replace(/^-+|-+$/g, '')
  }
}

// 分类数据管理
export class CategoryManager {
  private static instance: CategoryManager
  private categories: Map<string, Category[]> = new Map()

  static getInstance(): CategoryManager {
    if (!CategoryManager.instance) {
      CategoryManager.instance = new CategoryManager()
    }
    return CategoryManager.instance
  }

  async loadCategories(locale: string): Promise<Category[]> {
    if (this.categories.has(locale)) {
      return this.categories.get(locale)!
    }

    // 这里可以从文件或数据库加载分类数据
    // 暂时使用硬编码的分类结构
    const categories: Category[] = this.getDefaultCategories(locale)
    
    this.categories.set(locale, categories)
    return categories
  }

  private getDefaultCategories(locale: string): Category[] {
    if (locale === 'zh') {
      return [
        {
          id: 'medications',
          slug: 'medications',
          name: '用药指导',
          description: '关于G6PD缺乏症患者用药的专业指导',
          icon: 'pill',
          faqCount: 0,
          priority: 1,
          locale: 'zh',
          children: [
            {
              id: 'chinese-medicine',
              slug: 'chinese-medicine',
              name: '中药相关',
              description: '中药使用注意事项和禁忌',
              parentId: 'medications',
              faqCount: 0,
              priority: 1,
              locale: 'zh'
            },
            {
              id: 'oral-solutions',
              slug: 'oral-solutions',
              name: '口服液相关',
              description: '各类口服液的使用指导',
              parentId: 'medications',
              faqCount: 0,
              priority: 2,
              locale: 'zh'
            },
            {
              id: 'western-medicine',
              slug: 'western-medicine',
              name: '西药相关',
              description: '西药使用注意事项',
              parentId: 'medications',
              faqCount: 0,
              priority: 3,
              locale: 'zh'
            }
          ]
        },
        {
          id: 'diet',
          slug: 'diet',
          name: '饮食指导',
          description: 'G6PD缺乏症患者的饮食建议和禁忌',
          icon: 'utensils',
          faqCount: 0,
          priority: 2,
          locale: 'zh'
        },
        {
          id: 'symptoms',
          slug: 'symptoms',
          name: '症状识别',
          description: '如何识别和处理G6PD缺乏症相关症状',
          icon: 'stethoscope',
          faqCount: 0,
          priority: 3,
          locale: 'zh'
        },
        {
          id: 'treatment',
          slug: 'treatment',
          name: '治疗方案',
          description: 'G6PD缺乏症的治疗和管理方案',
          icon: 'heart-pulse',
          faqCount: 0,
          priority: 4,
          locale: 'zh'
        }
      ]
    } else {
      return [
        {
          id: 'medications',
          slug: 'medications',
          name: 'Medications',
          description: 'Professional guidance on medications for G6PD deficiency patients',
          icon: 'pill',
          faqCount: 0,
          priority: 1,
          locale: 'en',
          children: [
            {
              id: 'chinese-medicine',
              slug: 'chinese-medicine',
              name: 'Chinese Medicine',
              description: 'Precautions and contraindications for Chinese medicine',
              parentId: 'medications',
              faqCount: 0,
              priority: 1,
              locale: 'en'
            },
            {
              id: 'oral-solutions',
              slug: 'oral-solutions',
              name: 'Oral Solutions',
              description: 'Guidance for various oral solutions',
              parentId: 'medications',
              faqCount: 0,
              priority: 2,
              locale: 'en'
            },
            {
              id: 'western-medicine',
              slug: 'western-medicine',
              name: 'Western Medicine',
              description: 'Precautions for western medications',
              parentId: 'medications',
              faqCount: 0,
              priority: 3,
              locale: 'en'
            }
          ]
        },
        {
          id: 'diet',
          slug: 'diet',
          name: 'Diet',
          description: 'Dietary recommendations and restrictions for G6PD deficiency patients',
          icon: 'utensils',
          faqCount: 0,
          priority: 2,
          locale: 'en'
        },
        {
          id: 'symptoms',
          slug: 'symptoms',
          name: 'Symptoms',
          description: 'How to recognize and handle G6PD deficiency related symptoms',
          icon: 'stethoscope',
          faqCount: 0,
          priority: 3,
          locale: 'en'
        },
        {
          id: 'treatment',
          slug: 'treatment',
          name: 'Treatment',
          description: 'Treatment and management options for G6PD deficiency',
          icon: 'heart-pulse',
          faqCount: 0,
          priority: 4,
          locale: 'en'
        }
      ]
    }
  }
}

// 导出单例实例
export const faqManager = FAQManager.getInstance()
export const categoryManager = CategoryManager.getInstance()
