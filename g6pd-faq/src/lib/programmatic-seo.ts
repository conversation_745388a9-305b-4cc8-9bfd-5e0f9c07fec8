import { 
  FAQ, 
  LongTailKeyword, 
  ContentTemplate, 
  KeywordDataSource, 
  ContentGenerationTask, 
  GeneratedContent,
  SEOMetadata,
  ContentVariant 
} from './types'
import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'

/**
 * 程序化SEO管理器
 * 负责长尾关键词处理、内容自动生成和SEO优化
 */
export class ProgrammaticSEOManager {
  private static instance: ProgrammaticSEOManager
  private keywordSources: Map<string, KeywordDataSource> = new Map()
  private templates: Map<string, ContentTemplate> = new Map()
  private generationTasks: Map<string, ContentGenerationTask> = new Map()

  static getInstance(): ProgrammaticSEOManager {
    if (!ProgrammaticSEOManager.instance) {
      ProgrammaticSEOManager.instance = new ProgrammaticSEOManager()
    }
    return ProgrammaticSEOManager.instance
  }

  /**
   * 加载长尾关键词数据源
   */
  async loadKeywordDataSources(): Promise<KeywordDataSource[]> {
    const sources: KeywordDataSource[] = []
    
    // 加载中药长尾词
    const chineseMedicineSource: KeywordDataSource = {
      id: 'chinese-medicine-keywords',
      name: '蚕豆病中药长尾词',
      description: '中药相关的长尾关键词数据',
      filePath: '../蚕豆病中药长尾词.md',
      format: 'md',
      lastUpdated: new Date().toISOString(),
      totalKeywords: 0,
      processedKeywords: 0,
      status: 'pending'
    }

    // 加载口服液长尾词
    const oralSolutionSource: KeywordDataSource = {
      id: 'oral-solution-keywords',
      name: '蚕豆病口服液长尾词',
      description: '口服液相关的长尾关键词数据',
      filePath: '../蚕豆病口服液长尾词.md',
      format: 'md',
      lastUpdated: new Date().toISOString(),
      totalKeywords: 0,
      processedKeywords: 0,
      status: 'pending'
    }

    sources.push(chineseMedicineSource, oralSolutionSource)
    
    // 缓存数据源
    sources.forEach(source => {
      this.keywordSources.set(source.id, source)
    })

    return sources
  }

  /**
   * 解析长尾关键词文件
   */
  async parseKeywordFile(filePath: string): Promise<LongTailKeyword[]> {
    const fullPath = path.join(process.cwd(), filePath)
    
    if (!fs.existsSync(fullPath)) {
      throw new Error(`关键词文件不存在: ${filePath}`)
    }

    const content = fs.readFileSync(fullPath, 'utf8')
    const lines = content.split('\n').filter(line => line.trim())
    const keywords: LongTailKeyword[] = []

    // 跳过标题行
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim()
      if (!line) continue

      const parts = line.split('\t')
      if (parts.length >= 3) {
        const keyword = parts[0].trim()
        const searchVolume = parseInt(parts[2]) || 0
        
        // 只处理有搜索量的关键词
        if (searchVolume > 0) {
          keywords.push({
            keyword,
            searchVolume,
            difficulty: this.calculateKeywordDifficulty(searchVolume),
            intent: this.determineSearchIntent(keyword),
            variations: this.generateKeywordVariations(keyword),
            relatedTerms: this.extractRelatedTerms(keyword),
            source: filePath
          })
        }
      }
    }

    return keywords.sort((a, b) => b.searchVolume - a.searchVolume)
  }

  /**
   * 计算关键词难度
   */
  private calculateKeywordDifficulty(searchVolume: number): number {
    if (searchVolume > 100000) return 80
    if (searchVolume > 50000) return 60
    if (searchVolume > 10000) return 40
    return 20
  }

  /**
   * 判断搜索意图
   */
  private determineSearchIntent(keyword: string): 'informational' | 'navigational' | 'transactional' | 'commercial' {
    const informationalPatterns = ['什么', '如何', '怎么', '为什么', '哪些', '能不能', '可以', '是否']
    const transactionalPatterns = ['购买', '价格', '多少钱', '哪里买']
    
    if (informationalPatterns.some(pattern => keyword.includes(pattern))) {
      return 'informational'
    }
    
    if (transactionalPatterns.some(pattern => keyword.includes(pattern))) {
      return 'transactional'
    }
    
    return 'informational' // 默认为信息型
  }

  /**
   * 生成关键词变体
   */
  private generateKeywordVariations(keyword: string): string[] {
    const variations: string[] = []
    
    // 基础变体模式
    const patterns = [
      keyword.replace('蚕豆病', 'G6PD缺乏症'),
      keyword.replace('不能吃', '禁用'),
      keyword.replace('不能吃', '不可以吃'),
      keyword.replace('可以吃', '能吃'),
      keyword.replace('哪些', '什么'),
      keyword.replace('什么', '哪些')
    ]
    
    patterns.forEach(pattern => {
      if (pattern !== keyword && !variations.includes(pattern)) {
        variations.push(pattern)
      }
    })
    
    return variations
  }

  /**
   * 提取相关术语
   */
  private extractRelatedTerms(keyword: string): string[] {
    const relatedTerms: string[] = []
    
    // 医学相关术语
    const medicalTerms = ['溶血', '贫血', '黄疸', '血红蛋白', '红细胞']
    // 药物相关术语
    const medicineTerms = ['中药', '西药', '口服液', '药物', '成分', '禁忌']
    
    if (keyword.includes('中药')) {
      relatedTerms.push(...medicalTerms, '中医', '草药', '方剂')
    }
    
    if (keyword.includes('口服液')) {
      relatedTerms.push(...medicalTerms, '液体药物', '儿童用药')
    }
    
    return relatedTerms
  }

  /**
   * 生成SEO元数据
   */
  generateSEOMetadata(keyword: string, content: string, locale: 'zh' | 'en'): SEOMetadata {
    // 确保参数有效
    const safeKeyword = keyword || '未知关键词'
    const safeContent = content || ''

    const focusKeyword = safeKeyword
    const semanticKeywords = this.extractSemanticKeywords(safeContent, locale)

    return {
      title: this.generateSEOTitle(safeKeyword, locale),
      description: this.generateSEODescription(safeKeyword, safeContent, locale),
      keywords: [focusKeyword, ...semanticKeywords],
      focusKeyword,
      semanticKeywords,
      readabilityScore: this.calculateReadabilityScore(safeContent, locale),
      keywordDensity: this.calculateKeywordDensity(safeContent, focusKeyword),
      contentLength: safeContent.length
    }
  }

  /**
   * 生成SEO标题
   */
  private generateSEOTitle(keyword: string, locale: 'zh' | 'en'): string {
    const siteName = locale === 'zh' ? 'G6PD缺乏症FAQ' : 'G6PD Deficiency FAQ'
    
    // 确保标题包含关键词且长度适中
    if (keyword.length > 30) {
      const shortKeyword = keyword.substring(0, 30) + '...'
      return `${shortKeyword} - ${siteName}`
    }
    
    return `${keyword} - ${siteName}`
  }

  /**
   * 生成SEO描述
   */
  private generateSEODescription(keyword: string, content: string, locale: 'zh' | 'en'): string {
    // 确保content是字符串
    const safeContent = content || ''

    // 从内容中提取前150个字符作为描述
    const excerpt = safeContent.length > 150
      ? safeContent.substring(0, 150).replace(/\n/g, ' ').trim() + '...'
      : safeContent.replace(/\n/g, ' ').trim()

    if (locale === 'zh') {
      return `${keyword}的详细解答。${excerpt}了解更多G6PD缺乏症相关信息。`
    } else {
      return `Detailed answer about ${keyword}. ${excerpt}Learn more about G6PD deficiency.`
    }
  }

  /**
   * 提取语义关键词
   */
  private extractSemanticKeywords(content: string, locale: 'zh' | 'en'): string[] {
    const keywords: string[] = []
    
    if (locale === 'zh') {
      const patterns = [
        'G6PD缺乏症', '蚕豆病', '溶血性贫血', '葡萄糖-6-磷酸脱氢酶',
        '用药安全', '药物禁忌', '中药', '西药', '口服液',
        '症状', '治疗', '预防', '饮食', '注意事项'
      ]
      
      patterns.forEach(pattern => {
        if (pattern && content && content.includes(pattern) && !keywords.includes(pattern)) {
          keywords.push(pattern)
        }
      })
    }
    
    return keywords.slice(0, 10) // 限制数量
  }

  /**
   * 计算可读性评分
   */
  private calculateReadabilityScore(content: string, locale: 'zh' | 'en'): number {
    // 确保content是字符串
    if (!content || typeof content !== 'string') return 0

    // 简化的可读性评分算法
    const sentences = content.split(/[。！？.!?]/).filter(s => s.trim())
    const words = content.split(/\s+/).filter(w => w.trim())

    if (sentences.length === 0 || words.length === 0) return 0

    const avgWordsPerSentence = words.length / sentences.length

    // 中文和英文的评分标准不同
    if (locale === 'zh') {
      // 中文：每句15-25字为最佳
      if (avgWordsPerSentence >= 15 && avgWordsPerSentence <= 25) return 90
      if (avgWordsPerSentence >= 10 && avgWordsPerSentence <= 30) return 75
      return 60
    } else {
      // 英文：每句15-20词为最佳
      if (avgWordsPerSentence >= 15 && avgWordsPerSentence <= 20) return 90
      if (avgWordsPerSentence >= 10 && avgWordsPerSentence <= 25) return 75
      return 60
    }
  }

  /**
   * 计算关键词密度
   */
  private calculateKeywordDensity(content: string, keyword: string): number {
    // 确保参数是字符串
    if (!content || typeof content !== 'string' || !keyword || typeof keyword !== 'string') {
      return 0
    }

    const words = content.toLowerCase().split(/\s+/)
    const keywordWords = keyword.toLowerCase().split(/\s+/)

    let matches = 0
    for (let i = 0; i <= words.length - keywordWords.length; i++) {
      const slice = words.slice(i, i + keywordWords.length)
      if (slice.join(' ') === keywordWords.join(' ')) {
        matches++
      }
    }

    return words.length > 0 ? (matches / words.length) * 100 : 0
  }
}

// 导出单例实例
export const programmaticSEOManager = ProgrammaticSEOManager.getInstance()
