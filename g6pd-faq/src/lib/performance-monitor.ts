import { FAQ } from './types'
import { seoOptimizer } from './seo-optimizer'
import { internalLinkingManager } from './internal-linking'
import { urlStructureManager } from './url-structure'
import fs from 'fs'
import path from 'path'

/**
 * 程序化SEO性能监控系统
 */

export interface PerformanceMetrics {
  timestamp: string
  totalFAQs: number
  seoScores: {
    average: number
    median: number
    min: number
    max: number
    distribution: Record<string, number>
  }
  contentQuality: {
    averageWordCount: number
    averageKeywordDensity: number
    duplicateContentCount: number
    lowQualityCount: number
  }
  technicalSEO: {
    validSitemaps: number
    totalSitemapUrls: number
    brokenInternalLinks: number
    missingMetaTags: number
    structuredDataErrors: number
  }
  userExperience: {
    averageLoadTime: number
    mobileOptimizationScore: number
    accessibilityScore: number
    coreWebVitals: {
      lcp: number // Largest Contentful Paint
      fid: number // First Input Delay
      cls: number // Cumulative Layout Shift
    }
  }
  searchPerformance: {
    indexedPages: number
    averagePosition: number
    clickThroughRate: number
    impressions: number
    clicks: number
  }
}

export interface PerformanceAlert {
  type: 'error' | 'warning' | 'info'
  category: 'seo' | 'content' | 'technical' | 'performance'
  message: string
  severity: number
  timestamp: string
  affectedItems: string[]
  recommendations: string[]
}

export interface PerformanceReport {
  summary: PerformanceMetrics
  alerts: PerformanceAlert[]
  trends: {
    period: string
    metrics: PerformanceMetrics[]
  }
  recommendations: {
    priority: 'high' | 'medium' | 'low'
    category: string
    title: string
    description: string
    impact: string
    effort: string
    actions: string[]
  }[]
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metricsHistory: PerformanceMetrics[] = []
  private alertsHistory: PerformanceAlert[] = []

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  /**
   * 收集性能指标
   */
  async collectMetrics(faqs: FAQ[]): Promise<PerformanceMetrics> {
    console.log('📊 收集性能指标...')

    const timestamp = new Date().toISOString()
    
    // 1. SEO评分统计
    const seoScores = await this.calculateSEOScores(faqs)
    
    // 2. 内容质量分析
    const contentQuality = await this.analyzeContentQuality(faqs)
    
    // 3. 技术SEO检查
    const technicalSEO = await this.checkTechnicalSEO(faqs)
    
    // 4. 用户体验指标（模拟数据）
    const userExperience = await this.measureUserExperience()
    
    // 5. 搜索性能指标（模拟数据）
    const searchPerformance = await this.getSearchPerformance()

    const metrics: PerformanceMetrics = {
      timestamp,
      totalFAQs: faqs.length,
      seoScores,
      contentQuality,
      technicalSEO,
      userExperience,
      searchPerformance
    }

    // 保存到历史记录
    this.metricsHistory.push(metrics)
    
    // 保持最近30天的数据
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    this.metricsHistory = this.metricsHistory.filter(
      m => new Date(m.timestamp) > thirtyDaysAgo
    )

    return metrics
  }

  /**
   * 计算SEO评分
   */
  private async calculateSEOScores(faqs: FAQ[]): Promise<PerformanceMetrics['seoScores']> {
    const scores: number[] = []
    
    for (const faq of faqs) {
      const report = seoOptimizer.generatePerformanceReport(faq, 'zh')
      scores.push(report.seoScore)
    }

    scores.sort((a, b) => a - b)
    
    const average = scores.reduce((sum, score) => sum + score, 0) / scores.length
    const median = scores[Math.floor(scores.length / 2)]
    const min = scores[0]
    const max = scores[scores.length - 1]

    // 分布统计
    const distribution: Record<string, number> = {
      'excellent': scores.filter(s => s >= 90).length,
      'good': scores.filter(s => s >= 70 && s < 90).length,
      'fair': scores.filter(s => s >= 50 && s < 70).length,
      'poor': scores.filter(s => s < 50).length
    }

    return {
      average: Math.round(average),
      median: Math.round(median),
      min,
      max,
      distribution
    }
  }

  /**
   * 分析内容质量
   */
  private async analyzeContentQuality(faqs: FAQ[]): Promise<PerformanceMetrics['contentQuality']> {
    let totalWordCount = 0
    let totalKeywordDensity = 0
    let duplicateContentCount = 0
    let lowQualityCount = 0

    const contentHashes = new Set<string>()

    for (const faq of faqs) {
      // 字数统计
      const wordCount = this.countWords(faq.answer)
      totalWordCount += wordCount

      // 关键词密度
      const seoData = seoOptimizer.optimizeFAQPage(faq, 'zh')
      totalKeywordDensity += seoData.keywordDensity.density

      // 重复内容检测
      const contentHash = this.generateContentHash(faq.answer)
      if (contentHashes.has(contentHash)) {
        duplicateContentCount++
      } else {
        contentHashes.add(contentHash)
      }

      // 低质量内容检测
      if (wordCount < 100 || seoData.keywordDensity.density < 0.5) {
        lowQualityCount++
      }
    }

    return {
      averageWordCount: Math.round(totalWordCount / faqs.length),
      averageKeywordDensity: Math.round((totalKeywordDensity / faqs.length) * 100) / 100,
      duplicateContentCount,
      lowQualityCount
    }
  }

  /**
   * 检查技术SEO
   */
  private async checkTechnicalSEO(faqs: FAQ[]): Promise<PerformanceMetrics['technicalSEO']> {
    let validSitemaps = 0
    let totalSitemapUrls = 0
    let brokenInternalLinks = 0
    let missingMetaTags = 0
    let structuredDataErrors = 0

    // 检查站点地图
    const publicDir = path.join(process.cwd(), 'public')
    const sitemapFiles = ['sitemap.xml', 'sitemap-main.xml']
    
    for (const file of sitemapFiles) {
      const filePath = path.join(publicDir, file)
      if (fs.existsSync(filePath)) {
        validSitemaps++
        const content = fs.readFileSync(filePath, 'utf8')
        const urlMatches = content.match(/<loc>/g)
        if (urlMatches) {
          totalSitemapUrls += urlMatches.length
        }
      }
    }

    // 检查FAQ的SEO问题
    for (const faq of faqs) {
      const seoData = seoOptimizer.optimizeFAQPage(faq, 'zh')
      
      // 检查meta标签
      if (!faq.seo?.title || !faq.seo?.description) {
        missingMetaTags++
      }

      // 检查结构化数据
      if (seoData.structuredData.length === 0) {
        structuredDataErrors++
      }

      // 检查内部链接（简化检查）
      const linkingStrategy = internalLinkingManager.generateLinkingStrategy(faq, 'zh')
      if (linkingStrategy.relatedFAQs.length === 0) {
        brokenInternalLinks++
      }
    }

    return {
      validSitemaps,
      totalSitemapUrls,
      brokenInternalLinks,
      missingMetaTags,
      structuredDataErrors
    }
  }

  /**
   * 测量用户体验指标（模拟）
   */
  private async measureUserExperience(): Promise<PerformanceMetrics['userExperience']> {
    // 在实际应用中，这些数据应该来自真实的性能监控工具
    return {
      averageLoadTime: Math.random() * 2 + 1, // 1-3秒
      mobileOptimizationScore: Math.floor(Math.random() * 20) + 80, // 80-100
      accessibilityScore: Math.floor(Math.random() * 15) + 85, // 85-100
      coreWebVitals: {
        lcp: Math.random() * 1.5 + 1.5, // 1.5-3秒
        fid: Math.random() * 50 + 50, // 50-100ms
        cls: Math.random() * 0.1 + 0.05 // 0.05-0.15
      }
    }
  }

  /**
   * 获取搜索性能指标（模拟）
   */
  private async getSearchPerformance(): Promise<PerformanceMetrics['searchPerformance']> {
    // 在实际应用中，这些数据应该来自Google Search Console API
    return {
      indexedPages: Math.floor(Math.random() * 50) + 50, // 50-100
      averagePosition: Math.random() * 20 + 10, // 10-30
      clickThroughRate: Math.random() * 0.05 + 0.02, // 2-7%
      impressions: Math.floor(Math.random() * 10000) + 5000, // 5000-15000
      clicks: Math.floor(Math.random() * 500) + 200 // 200-700
    }
  }

  /**
   * 生成性能警报
   */
  generateAlerts(metrics: PerformanceMetrics): PerformanceAlert[] {
    const alerts: PerformanceAlert[] = []
    const timestamp = new Date().toISOString()

    // SEO评分警报
    if (metrics.seoScores.average < 70) {
      alerts.push({
        type: 'warning',
        category: 'seo',
        message: `平均SEO评分过低: ${metrics.seoScores.average}/100`,
        severity: 75,
        timestamp,
        affectedItems: [`${metrics.seoScores.distribution.poor + metrics.seoScores.distribution.fair} 个FAQ`],
        recommendations: [
          '优化meta标签和描述',
          '改善关键词密度',
          '增加内部链接',
          '完善结构化数据'
        ]
      })
    }

    // 内容质量警报
    if (metrics.contentQuality.duplicateContentCount > 0) {
      alerts.push({
        type: 'error',
        category: 'content',
        message: `发现重复内容: ${metrics.contentQuality.duplicateContentCount} 个FAQ`,
        severity: 90,
        timestamp,
        affectedItems: [`${metrics.contentQuality.duplicateContentCount} 个重复FAQ`],
        recommendations: [
          '重写重复内容',
          '使用canonical标签',
          '合并相似内容',
          '增加内容差异化'
        ]
      })
    }

    if (metrics.contentQuality.lowQualityCount > metrics.totalFAQs * 0.2) {
      alerts.push({
        type: 'warning',
        category: 'content',
        message: `低质量内容过多: ${metrics.contentQuality.lowQualityCount} 个FAQ`,
        severity: 70,
        timestamp,
        affectedItems: [`${metrics.contentQuality.lowQualityCount} 个低质量FAQ`],
        recommendations: [
          '扩展内容长度',
          '提高关键词相关性',
          '增加专业性内容',
          '改善内容结构'
        ]
      })
    }

    // 技术SEO警报
    if (metrics.technicalSEO.missingMetaTags > 0) {
      alerts.push({
        type: 'error',
        category: 'technical',
        message: `缺失meta标签: ${metrics.technicalSEO.missingMetaTags} 个FAQ`,
        severity: 85,
        timestamp,
        affectedItems: [`${metrics.technicalSEO.missingMetaTags} 个FAQ缺失meta标签`],
        recommendations: [
          '补充title和description标签',
          '优化meta标签长度',
          '确保标签唯一性',
          '添加Open Graph标签'
        ]
      })
    }

    // 用户体验警报
    if (metrics.userExperience.averageLoadTime > 3) {
      alerts.push({
        type: 'warning',
        category: 'performance',
        message: `页面加载时间过长: ${metrics.userExperience.averageLoadTime.toFixed(2)}秒`,
        severity: 80,
        timestamp,
        affectedItems: ['所有页面'],
        recommendations: [
          '优化图片大小',
          '启用CDN',
          '压缩CSS和JavaScript',
          '使用浏览器缓存'
        ]
      })
    }

    // 保存到历史记录
    this.alertsHistory.push(...alerts)

    return alerts
  }

  /**
   * 生成性能报告
   */
  generateReport(metrics: PerformanceMetrics): PerformanceReport {
    const alerts = this.generateAlerts(metrics)
    
    // 生成趋势数据
    const trends = {
      period: '30天',
      metrics: this.metricsHistory.slice(-30)
    }

    // 生成建议
    const recommendations = this.generateRecommendations(metrics, alerts)

    return {
      summary: metrics,
      alerts,
      trends,
      recommendations
    }
  }

  /**
   * 生成优化建议
   */
  private generateRecommendations(
    metrics: PerformanceMetrics, 
    alerts: PerformanceAlert[]
  ): PerformanceReport['recommendations'] {
    const recommendations: PerformanceReport['recommendations'] = []

    // 基于SEO评分的建议
    if (metrics.seoScores.average < 80) {
      recommendations.push({
        priority: 'high',
        category: 'SEO优化',
        title: '提升整体SEO表现',
        description: '当前平均SEO评分偏低，需要系统性优化',
        impact: '提升搜索排名和流量',
        effort: '中等',
        actions: [
          '优化所有页面的meta标签',
          '完善结构化数据标记',
          '改善内部链接策略',
          '提高关键词相关性'
        ]
      })
    }

    // 基于内容质量的建议
    if (metrics.contentQuality.averageWordCount < 300) {
      recommendations.push({
        priority: 'medium',
        category: '内容优化',
        title: '扩展内容深度',
        description: '当前内容长度偏短，影响SEO效果',
        impact: '提升内容权威性和搜索排名',
        effort: '高',
        actions: [
          '为每个FAQ增加详细解释',
          '添加相关案例和示例',
          '包含更多专业医学信息',
          '增加常见问题的变体'
        ]
      })
    }

    // 基于技术问题的建议
    if (alerts.some(a => a.category === 'technical' && a.type === 'error')) {
      recommendations.push({
        priority: 'high',
        category: '技术修复',
        title: '修复技术SEO问题',
        description: '存在影响搜索引擎抓取的技术问题',
        impact: '确保页面被正确索引',
        effort: '低',
        actions: [
          '修复所有缺失的meta标签',
          '验证结构化数据格式',
          '检查并修复内部链接',
          '优化站点地图结构'
        ]
      })
    }

    return recommendations
  }

  /**
   * 保存性能数据
   */
  async savePerformanceData(report: PerformanceReport): Promise<void> {
    const outputDir = path.join(process.cwd(), 'src/data/performance')
    
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }

    const timestamp = new Date().toISOString().split('T')[0]
    const filename = `performance-report-${timestamp}.json`
    const filePath = path.join(outputDir, filename)

    fs.writeFileSync(filePath, JSON.stringify(report, null, 2), 'utf8')
    
    // 保存最新报告
    const latestPath = path.join(outputDir, 'latest-report.json')
    fs.writeFileSync(latestPath, JSON.stringify(report, null, 2), 'utf8')
  }

  /**
   * 工具方法：计算字数
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).length
  }

  /**
   * 工具方法：生成内容哈希
   */
  private generateContentHash(content: string): string {
    // 简化的哈希函数
    let hash = 0
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return hash.toString()
  }
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance()
