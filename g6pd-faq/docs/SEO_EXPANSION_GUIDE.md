# 程序化SEO扩展指导文档

## 📋 概述

本文档详细说明如何在现有的G6PD FAQ网站中添加新的长尾关键词并自动生成新页面。基于已完成的程序化SEO系统，您可以轻松扩展网站内容，提高搜索引擎覆盖率。

## 🎯 系统架构回顾

当前程序化SEO系统包含以下核心组件：

- **关键词处理器** (`src/lib/keyword-processor.ts`)
- **内容生成器** (`src/lib/content-generator.ts`) 
- **SEO优化器** (`src/lib/seo-optimizer.ts`)
- **URL结构管理器** (`src/lib/url-structure.ts`)
- **站点地图生成器** (`src/lib/sitemap-generator.ts`)

## 🚀 快速开始：添加新长尾词

### 步骤1: 准备关键词数据

1. **创建关键词文件**
   ```bash
   # 在 src/data/keywords/ 目录下创建新的关键词文件
   touch src/data/keywords/新分类长尾词.md
   ```

2. **关键词文件格式**
   ```markdown
   # 新分类长尾词数据
   
   ## 元数据
   - 分类: 新分类名称
   - 语言: zh/en
   - 优先级: high/medium/low
   - 更新时间: 2025-07-02
   
   ## 关键词列表
   
   ### 高搜索量关键词 (>1000/月)
   - 关键词1 | 搜索量 | 竞争度 | 相关性
   - 关键词2 | 搜索量 | 竞争度 | 相关性
   
   ### 中等搜索量关键词 (100-1000/月)
   - 关键词3 | 搜索量 | 竞争度 | 相关性
   
   ### 长尾关键词 (<100/月)
   - 关键词4 | 搜索量 | 竞争度 | 相关性
   ```

### 步骤2: 处理关键词数据

1. **运行关键词处理脚本**
   ```bash
   npm run process-keywords
   ```

2. **验证处理结果**
   ```bash
   # 检查处理后的关键词文件
   ls src/data/processed-keywords/
   
   # 查看处理报告
   cat src/data/execution-reports/keyword-processing-*.json
   ```

### 步骤3: 配置内容模板

1. **创建新的内容模板**
   ```typescript
   // 在 src/lib/content-generator.ts 中添加新模板
   const newCategoryTemplate: ContentTemplate = {
     id: 'new-category-template',
     name: '新分类模板',
     locale: 'zh',
     category: '新分类',
     structure: {
       question: '关于{keyword}的常见问题',
       shortAnswer: '{keyword}的简要说明...',
       detailedAnswer: '{keyword}的详细解答...',
       precautions: '使用{keyword}时的注意事项...',
       recommendations: '关于{keyword}的专业建议...'
     }
   }
   ```

2. **注册新模板**
   ```typescript
   // 在 ContentGenerator 构造函数中添加
   this.templates.set(newCategoryTemplate.id, newCategoryTemplate)
   ```

### 步骤4: 生成内容

1. **运行内容生成脚本**
   ```bash
   npm run generate-content
   ```

2. **检查生成的内容**
   ```bash
   # 查看生成的FAQ文件
   ls src/data/generated-faqs/
   
   # 检查内容质量报告
   cat src/data/execution-reports/content-generation-*.json
   ```

### 步骤5: 更新站点地图

## 📁 文件结构说明

```
src/data/
├── keywords/                    # 原始关键词数据
│   ├── 蚕豆病中药长尾词.md         # 中药相关关键词
│   ├── 蚕豆病口服液长尾词.md       # 口服液相关关键词
│   └── 新分类长尾词.md            # 新添加的关键词
├── processed-keywords/          # 处理后的关键词
│   ├── chinese-medicine-zh.json
│   ├── oral-solutions-zh.json
│   └── new-category-zh.json
├── generated-faqs/              # 生成的FAQ内容
│   ├── chinese-medicine/
│   ├── oral-solutions/
│   └── new-category/
└── execution-reports/           # 执行报告
    ├── keyword-processing-*.json
    ├── content-generation-*.json
    └── sitemap-generation-*.json
```

## 🔧 高级配置

### 自定义URL结构

```typescript
// 在 src/lib/url-structure.ts 中配置新的URL模式
const newCategoryURLPattern = {
  pattern: '/faq/new-category/{slug}',
  locale: 'zh',
  category: 'new-category',
  priority: 0.8
}
```

### SEO优化配置

```typescript
// 在 src/lib/seo-optimizer.ts 中添加新分类的SEO规则
const newCategorySEOConfig = {
  titleTemplate: '{keyword} - G6PD缺乏症FAQ',
  descriptionTemplate: '了解{keyword}与G6PD缺乏症的关系...',
  keywordDensity: { min: 2, max: 6 },
  contentLength: { min: 300, max: 800 }
}
```

## 📊 监控和优化

### 性能监控

```bash
# 运行综合测试
npm run test-all

# 查看性能报告
cat src/data/performance/seo-performance-*.json
```

### 内容质量检查

```bash
# 检查SEO优化效果
npm run test-seo

# 验证导航结构
npm run test-navigation
```

## 🚨 注意事项

### 内容质量控制

1. **医疗内容合规**
   - 所有医疗相关内容必须包含免责声明
   - 建议用户咨询专业医生
   - 避免绝对性的医疗建议

2. **SEO最佳实践**
   - 避免关键词堆砌（密度控制在2-6%）
   - 保持内容原创性和差异化
   - 确保页面加载速度优化

3. **技术要求**
   - 新增页面必须支持多语言
   - 确保移动端适配
   - 维护内部链接结构

### 常见问题排查

1. **关键词处理失败**
   ```bash
   # 检查关键词文件格式
   # 确保编码为UTF-8
   # 验证Markdown语法
   ```

2. **内容生成错误**
   ```bash
   # 检查模板配置
   # 验证API调用限制
   # 查看错误日志
   ```

3. **站点地图生成问题**
   ```bash
   # 检查URL结构配置
   # 验证文件权限
   # 确保输出目录存在
   ```

## 📈 扩展建议

### 批量处理

对于大量关键词，建议：

1. **分批处理**
   ```bash
   # 每次处理50-100个关键词
   npm run process-keywords -- --batch-size=50
   ```

2. **定时任务**
   ```bash
   # 设置cron任务定期更新
   0 2 * * 0 cd /path/to/project && npm run run-all-tasks
   ```

### 自动化流程

```bash
# 完整的自动化流程
npm run run-all-tasks

# 仅执行必需任务
npm run run-all-tasks -- --skip-optional

# 查看执行计划
npm run run-all-tasks -- --dry-run
```

## 📞 技术支持

如遇到问题，请：

1. 查看执行报告文件
2. 检查系统日志
3. 运行诊断命令
4. 参考现有成功案例

## 💡 实际示例：添加"G6PD与疫苗"分类

### 示例1: 创建疫苗相关关键词文件

```bash
# 创建新的关键词文件
cat > src/data/keywords/蚕豆病疫苗长尾词.md << 'EOF'
# G6PD缺乏症疫苗相关长尾词

## 元数据
- 分类: 疫苗接种
- 语言: zh
- 优先级: high
- 更新时间: 2025-07-02

## 关键词列表

### 高搜索量关键词 (>500/月)
- G6PD缺乏症可以打疫苗吗 | 800 | 中等 | 高
- 蚕豆病能接种疫苗吗 | 600 | 中等 | 高
- G6PD疫苗接种注意事项 | 500 | 低 | 高

### 中等搜索量关键词 (100-500/月)
- 蚕豆病打新冠疫苗 | 300 | 中等 | 高
- G6PD缺乏症疫苗禁忌 | 250 | 低 | 高
- 蚕豆病儿童疫苗接种 | 200 | 中等 | 高

### 长尾关键词 (<100/月)
- G6PD缺乏症可以打流感疫苗吗 | 80 | 低 | 高
- 蚕豆病患者疫苗接种指南 | 60 | 低 | 高
- G6PD疫苗接种前检查 | 50 | 低 | 中
EOF
```

### 示例2: 执行完整流程

```bash
# 1. 处理新的关键词数据
npm run process-keywords

# 2. 生成疫苗相关FAQ内容
npm run generate-content

# 3. 更新站点地图
npm run generate-sitemap

# 4. 运行测试验证
npm run test-all
```

### 示例3: 验证结果

```bash
# 检查生成的文件
ls src/data/processed-keywords/vaccines-zh.json
ls src/data/generated-faqs/vaccines/
ls public/sitemap-faq-vaccines-*.xml

# 查看生成的FAQ示例
cat src/data/generated-faqs/vaccines/g6pd-vaccine-safety.json
```

---

**最后更新**: 2025-07-02
**版本**: 1.0
**维护者**: G6PD FAQ开发团队
