# Hierarchical Sitemap Architecture Guide

## 🎯 Overview

This guide documents the hierarchical sitemap architecture implemented for the G6PD FAQ website. The system replaces the single sitemap.xml approach with a scalable, hierarchical structure that supports incremental updates and efficient content management.

## 📁 Architecture Structure

### Main Sitemap Index
- **File**: `sitemap.xml`
- **Purpose**: Main index file that references all sub-sitemaps
- **Google Submission**: Submit this file to Google Search Console

### Sub-Sitemaps
1. **Static Pages Sitemap** (`sitemap_static.xml`)
   - Homepage, about, contact, privacy, terms, search pages
   - Update frequency: Weekly
   - Priority: 0.8-1.0

2. **Category Pages Sitemap** (`sitemap_categories.xml`)
   - FAQ category pages, medication category pages
   - Update frequency: Daily
   - Priority: 0.7-0.8

3. **Programmatic SEO Sitemap** (`sitemap_programmatic.xml`)
   - Generated FAQ pages from long-tail keywords
   - Update frequency: Monthly
   - Priority: 0.9 (high-value content)

## 🔄 Incremental Update System

### How It Works
The system uses content hashing to detect actual changes:

```javascript
// Only updates files when content actually changes
if (shouldUpdateSitemap(filePath, newContent)) {
  fs.writeFileSync(filePath, newContent)
  console.log(`🔄 Content changed, updating: ${filename}`)
} else {
  console.log(`✅ No changes detected, skipping: ${filename}`)
}
```

### Benefits
- **Prevents unnecessary updates**: Only changed sitemaps are regenerated
- **Preserves file timestamps**: Unchanged files maintain their modification dates
- **Reduces Google crawl load**: Google only re-processes changed sitemaps
- **Faster builds**: Skips unchanged content processing

## 🚀 Usage Instructions

### Manual Generation
```bash
# Generate hierarchical sitemaps manually
npm run generate-hierarchical-sitemap

# Or run directly
node scripts/generate-hierarchical-sitemap.js
```

### Automatic Generation
Sitemaps are automatically generated during the build process:
```bash
npm run build  # Automatically runs postbuild sitemap generation
```

### Build Integration
The system is integrated into the build process via `package.json`:
```json
{
  "scripts": {
    "postbuild": "node scripts/generate-hierarchical-sitemap.js"
  }
}
```

## 📊 Content Management

### Adding New Static Pages
1. Add the new page route to `SITEMAP_CONFIG.static.urls` in the generation script
2. Run the sitemap generation script
3. Only the static sitemap will be updated

### Adding New Category Pages
1. Add the new category to `SITEMAP_CONFIG.categories.urls`
2. Run the sitemap generation script
3. Only the categories sitemap will be updated

### Adding New Programmatic SEO Content
1. Add new FAQ JSON files to `/src/data/generated-faqs/`
2. Run the sitemap generation script
3. Only the programmatic sitemap will be updated

## 🔍 Monitoring and Validation

### Verification Commands
```bash
# Check sitemap generation output
npm run generate-hierarchical-sitemap

# Verify file existence
ls -la public/sitemap*.xml

# Check file sizes
du -h public/sitemap*.xml

# Validate XML format
xmllint --noout public/sitemap.xml
```

### Expected Output
```
🚀 Starting hierarchical sitemap generation...
✅ No changes detected, skipping: sitemap_static.xml
✅ No changes detected, skipping: sitemap_categories.xml
📊 Loaded 11 programmatic SEO FAQs
⚠️ Skipping FAQ with invalid slug: - (ID: faq-1751385630634-蚕豆病-中药)
📊 Generated 10 valid programmatic SEO URLs
✅ No changes detected, skipping: sitemap_programmatic.xml
✅ No changes detected, skipping: sitemap.xml
✅ Hierarchical sitemap generation completed!
📊 Total files updated: 0/4
```

### URL Validation
Test that all sitemap URLs are accessible:
```bash
# Test main sitemap index
curl -I https://g6pd.site/sitemap.xml

# Test sub-sitemaps
curl -I https://g6pd.site/sitemap_static.xml
curl -I https://g6pd.site/sitemap_categories.xml
curl -I https://g6pd.site/sitemap_programmatic.xml
```

## 🐛 Troubleshooting

### Common Issues

#### Invalid Slugs
**Problem**: FAQ files with invalid slugs (empty, dash, etc.)
**Solution**: The system automatically filters these out with warnings
```
⚠️ Skipping FAQ with invalid slug: - (ID: faq-1751385630634-蚕豆病-中药)
```

#### Missing Generated FAQs Directory
**Problem**: `/src/data/generated-faqs/` directory not found
**Solution**: Ensure programmatic SEO content has been generated first
```bash
npm run generate-content
```

#### Build Failures
**Problem**: Sitemap generation fails during build
**Solution**: Check the generation script logs and fix any data issues

### Debug Mode
Enable verbose logging by modifying the script:
```javascript
console.log(`📊 Processing FAQ: ${faq.title} (${faq.slug})`)
```

## 📈 Performance Metrics

### File Sizes (Current)
- `sitemap.xml`: ~1KB (index)
- `sitemap_static.xml`: ~3KB (14 URLs)
- `sitemap_categories.xml`: ~4KB (22 URLs)
- `sitemap_programmatic.xml`: ~2KB (10 URLs)

### Scalability Projections
- **100 programmatic pages**: ~20KB programmatic sitemap
- **500 programmatic pages**: ~100KB programmatic sitemap
- **1000+ programmatic pages**: Consider further sub-categorization

## 🔧 Configuration

### Sitemap Configuration
Located in `scripts/generate-hierarchical-sitemap.js`:

```javascript
const SITEMAP_CONFIG = {
  static: {
    filename: 'sitemap_static.xml',
    changefreq: 'weekly',
    priority: 0.8
  },
  categories: {
    filename: 'sitemap_categories.xml', 
    changefreq: 'daily',
    priority: 0.7
  },
  programmatic: {
    filename: 'sitemap_programmatic.xml',
    changefreq: 'monthly',
    priority: 0.9
  }
}
```

### Robots.txt Integration
The system automatically updates `robots.txt` to reference all sitemaps:
```
Sitemap: https://g6pd.site/sitemap.xml
Sitemap: https://g6pd.site/sitemap_static.xml
Sitemap: https://g6pd.site/sitemap_categories.xml
Sitemap: https://g6pd.site/sitemap_programmatic.xml
```

## 🎯 Best Practices

### Content Updates
1. **Static content changes**: Update lastmod dates in configuration
2. **New categories**: Add to categories configuration
3. **Programmatic content**: Use the existing generation pipeline

### SEO Optimization
1. **Priority values**: Use 0.9+ for high-value programmatic content
2. **Update frequencies**: Match actual content update patterns
3. **URL encoding**: System automatically handles Chinese characters

### Maintenance
1. **Regular monitoring**: Check sitemap generation logs
2. **Google Search Console**: Monitor indexing status
3. **Performance tracking**: Monitor file sizes and generation times

## 🚀 Future Enhancements

### Planned Improvements
1. **Dynamic priority calculation**: Based on search volume and competition
2. **Automatic lastmod detection**: Based on file modification times
3. **Sitemap compression**: For large programmatic content sets
4. **Multi-language optimization**: Separate sitemaps per locale

### Scaling Considerations
- **1000+ pages**: Consider sub-categorizing programmatic content
- **Multiple content types**: Add new sitemap categories as needed
- **High-frequency updates**: Implement real-time sitemap updates

---

## 📝 Quick Reference: Adding New Long-Tail Keyword Pages

### Step-by-Step Process

#### 1. Generate New FAQ Content
```bash
# Add keywords to your keyword files
# Run content generation
npm run generate-content
```

#### 2. Verify Generated Files
```bash
# Check new files in generated-faqs directory
ls -la src/data/generated-faqs/*/

# Verify JSON structure
cat src/data/generated-faqs/category/new-keyword.json
```

#### 3. Update Sitemaps
```bash
# Generate updated sitemaps (only programmatic sitemap will update)
npm run generate-hierarchical-sitemap
```

#### 4. Validate Results
```bash
# Check generation output
# Should show: "📊 Generated X valid programmatic SEO URLs"
# Should show: "🔄 Content changed, updating: sitemap_programmatic.xml"

# Verify new URLs in sitemap
grep "new-keyword" public/sitemap_programmatic.xml
```

#### 5. Test Accessibility
```bash
# Test new page accessibility
curl -I https://g6pd.site/zh/faq/medications/category/new-keyword

# Verify sitemap accessibility
curl -I https://g6pd.site/sitemap_programmatic.xml
```

### Automatic Integration
- ✅ New FAQ files are automatically detected
- ✅ Invalid slugs are automatically filtered out
- ✅ URLs are automatically encoded for Chinese characters
- ✅ Sitemaps are automatically updated during build
- ✅ Google will discover new content via sitemap updates

### Monitoring New Content
```bash
# Monitor sitemap generation logs
npm run generate-hierarchical-sitemap | grep "📊\|🔄\|⚠️"

# Check programmatic sitemap size
wc -l public/sitemap_programmatic.xml

# Verify URL count
grep -c "<url>" public/sitemap_programmatic.xml
```
