# 程序化SEO快速参考

## 🚀 一键添加新长尾词

### 最简流程 (5分钟)

```bash
# 1. 创建关键词文件
echo "# 新分类长尾词
- 关键词1 | 搜索量 | 竞争度 | 相关性
- 关键词2 | 搜索量 | 竞争度 | 相关性" > src/data/keywords/新分类.md

# 2. 一键执行所有任务
npm run run-all-tasks

# 3. 验证结果
npm run test-all
```

## 📋 常用命令

| 命令 | 功能 | 用途 |
|------|------|------|
| `npm run process-keywords` | 处理关键词 | 新增关键词后 |
| `npm run generate-content` | 生成内容 | 创建FAQ页面 |
| `npm run generate-sitemap` | 生成站点地图 | SEO优化 |
| `npm run test-all` | 综合测试 | 验证功能 |
| `npm run run-all-tasks` | 完整流程 | 一键执行 |

## 📁 关键文件位置

```
src/data/keywords/           # 添加新关键词文件
src/data/generated-faqs/     # 查看生成的FAQ
public/sitemap-*.xml         # 检查站点地图
src/data/execution-reports/  # 查看执行报告
```

## ⚡ 故障排除

| 问题 | 解决方案 |
|------|----------|
| 关键词处理失败 | 检查文件格式和编码 |
| 内容生成错误 | 验证模板配置 |
| 站点地图问题 | 确保输出目录权限 |
| 测试失败 | 查看错误日志 |

## 📊 质量检查清单

- [ ] 关键词文件格式正确
- [ ] 内容生成成功
- [ ] 站点地图更新
- [ ] SEO测试通过
- [ ] 导航结构正常
- [ ] 移动端适配
- [ ] 页面加载速度

## 🎯 最佳实践

1. **批量处理**: 每次添加50-100个关键词
2. **定期更新**: 每周运行一次完整流程
3. **质量控制**: 确保内容原创性和医疗合规
4. **性能监控**: 定期检查SEO表现

---
💡 **提示**: 使用 `npm run run-all-tasks -- --dry-run` 预览执行计划
