#!/usr/bin/env tsx

import { seoOptimizer } from '../src/lib/seo-optimizer'
import { internalLinkingManager } from '../src/lib/internal-linking'
import { FAQ } from '../src/lib/types'
import fs from 'fs'
import path from 'path'

/**
 * SEO优化测试脚本
 */

async function main() {
  console.log('🔍 开始SEO优化测试...')
  
  try {
    // 1. 加载生成的FAQ数据
    const faqs = await loadGeneratedFAQs()
    console.log(`📚 加载了 ${faqs.length} 个FAQ`)
    
    if (faqs.length === 0) {
      console.log('⚠️  没有找到FAQ数据，请先运行内容生成')
      return
    }
    
    // 2. 初始化内部链接索引
    internalLinkingManager.initializeIndex(faqs)
    
    // 3. 测试SEO优化
    console.log('\n🎯 测试SEO优化...')
    await testSEOOptimization(faqs.slice(0, 3))
    
    // 4. 生成SEO报告
    console.log('\n📊 生成SEO性能报告...')
    await generateSEOReport(faqs)
    
    // 5. 测试关键词密度分析
    console.log('\n🔤 测试关键词密度分析...')
    await testKeywordDensityAnalysis(faqs.slice(0, 2))
    
    console.log('\n✅ SEO优化测试完成!')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
    process.exit(1)
  }
}

/**
 * 加载生成的FAQ数据
 */
async function loadGeneratedFAQs(): Promise<FAQ[]> {
  const faqs: FAQ[] = []
  const generatedDir = path.join(process.cwd(), 'src/data/generated-faqs')
  
  if (!fs.existsSync(generatedDir)) {
    return faqs
  }
  
  const sources = fs.readdirSync(generatedDir)
  
  for (const source of sources) {
    const sourcePath = path.join(generatedDir, source)
    if (fs.statSync(sourcePath).isDirectory()) {
      const files = fs.readdirSync(sourcePath)
        .filter(file => file.endsWith('.json') && file !== 'index.json')
      
      for (const file of files) {
        try {
          const filePath = path.join(sourcePath, file)
          const faqData = JSON.parse(fs.readFileSync(filePath, 'utf8'))
          faqs.push(faqData)
        } catch (error) {
          console.warn(`⚠️  无法加载FAQ文件: ${file}`)
        }
      }
    }
  }
  
  return faqs
}

/**
 * 测试SEO优化
 */
async function testSEOOptimization(faqs: FAQ[]) {
  for (const faq of faqs) {
    console.log(`\n📄 FAQ: ${faq.title}`)
    
    // 中文SEO优化
    const zhSEO = seoOptimizer.optimizeFAQPage(faq, 'zh')
    console.log(`  🇨🇳 中文SEO优化:`)
    console.log(`    📝 Meta标签数量: ${zhSEO.metaTags.length}`)
    console.log(`    🔗 Canonical URL: ${zhSEO.canonicalUrl}`)
    console.log(`    📊 结构化数据类型: ${zhSEO.structuredData.map(d => d['@type']).join(', ')}`)
    console.log(`    🎯 关键词密度: ${zhSEO.keywordDensity.density}% (${zhSEO.keywordDensity.focusKeyword})`)
    console.log(`    ⚠️  SEO建议数量: ${zhSEO.recommendations.length}`)
    
    // 显示前3个SEO建议
    if (zhSEO.recommendations.length > 0) {
      console.log(`    💡 主要建议:`)
      zhSEO.recommendations.slice(0, 3).forEach((rec, index) => {
        const icon = rec.type === 'error' ? '❌' : rec.type === 'warning' ? '⚠️' : '💡'
        console.log(`      ${icon} ${rec.message} (优先级: ${rec.priority})`)
      })
    }
    
    // Open Graph数据
    console.log(`    📱 Open Graph:`)
    console.log(`      标题: ${zhSEO.openGraph.title}`)
    console.log(`      描述: ${zhSEO.openGraph.description.substring(0, 50)}...`)
    console.log(`      类型: ${zhSEO.openGraph.type}`)
    
    // Twitter Card数据
    console.log(`    🐦 Twitter Card:`)
    console.log(`      卡片类型: ${zhSEO.twitterCard.card}`)
    console.log(`      标题: ${zhSEO.twitterCard.title}`)
  }
}

/**
 * 生成SEO性能报告
 */
async function generateSEOReport(faqs: FAQ[]) {
  const reports = faqs.map(faq => {
    const zhReport = seoOptimizer.generatePerformanceReport(faq, 'zh')
    const enReport = seoOptimizer.generatePerformanceReport(faq, 'en')
    
    return {
      faqId: faq.id,
      title: faq.title,
      category: faq.category,
      subcategory: faq.subcategory,
      searchVolume: faq.searchVolume,
      zh: zhReport,
      en: enReport
    }
  })
  
  // 计算总体统计
  const avgZhScore = reports.reduce((sum, r) => sum + r.zh.seoScore, 0) / reports.length
  const avgEnScore = reports.reduce((sum, r) => sum + r.en.seoScore, 0) / reports.length
  
  console.log(`📊 SEO性能总览:`)
  console.log(`  - 总FAQ数量: ${reports.length}`)
  console.log(`  - 中文平均SEO评分: ${Math.round(avgZhScore)}/100`)
  console.log(`  - 英文平均SEO评分: ${Math.round(avgEnScore)}/100`)
  
  // 按评分排序显示前5和后5
  const sortedByZhScore = reports.sort((a, b) => b.zh.seoScore - a.zh.seoScore)
  
  console.log(`\n🏆 SEO评分最高的FAQ (前5):`)
  sortedByZhScore.slice(0, 5).forEach((report, index) => {
    console.log(`  ${index + 1}. ${report.title} - ${report.zh.seoScore}/100`)
    if (report.zh.strengths.length > 0) {
      console.log(`     ✅ 优势: ${report.zh.strengths.slice(0, 2).join(', ')}`)
    }
  })
  
  console.log(`\n⚠️  需要改进的FAQ (后5):`)
  sortedByZhScore.slice(-5).reverse().forEach((report, index) => {
    console.log(`  ${index + 1}. ${report.title} - ${report.zh.seoScore}/100`)
    if (report.zh.improvements.length > 0) {
      console.log(`     🔧 改进: ${report.zh.improvements.slice(0, 2).join(', ')}`)
    }
  })
  
  // 问题类型统计
  const issueStats: Record<string, number> = {}
  reports.forEach(report => {
    report.zh.issues.forEach(issue => {
      issueStats[issue.category] = (issueStats[issue.category] || 0) + 1
    })
  })
  
  console.log(`\n📈 常见SEO问题统计:`)
  Object.entries(issueStats)
    .sort((a, b) => b[1] - a[1])
    .forEach(([category, count]) => {
      console.log(`  - ${category}: ${count} 个FAQ`)
    })
  
  // 保存详细报告
  const detailedReport = {
    generatedAt: new Date().toISOString(),
    summary: {
      totalFAQs: reports.length,
      averageZhScore: Math.round(avgZhScore),
      averageEnScore: Math.round(avgEnScore),
      issueStats
    },
    faqs: reports
  }
  
  const outputPath = path.join(process.cwd(), 'src/data/seo-performance-report.json')
  fs.writeFileSync(outputPath, JSON.stringify(detailedReport, null, 2), 'utf8')
  console.log(`💾 详细SEO报告已保存到: ${outputPath}`)
}

/**
 * 测试关键词密度分析
 */
async function testKeywordDensityAnalysis(faqs: FAQ[]) {
  for (const faq of faqs) {
    console.log(`\n🔤 FAQ: ${faq.title}`)
    
    const zhSEO = seoOptimizer.optimizeFAQPage(faq, 'zh')
    const keywordReport = zhSEO.keywordDensity
    
    console.log(`  📊 关键词密度分析:`)
    console.log(`    🎯 主关键词: "${keywordReport.focusKeyword}"`)
    console.log(`    📈 密度: ${keywordReport.density}% (${keywordReport.occurrences}/${keywordReport.totalWords})`)
    console.log(`    💡 建议: ${keywordReport.recommendations.join(', ')}`)
    
    if (keywordReport.relatedKeywords.length > 0) {
      console.log(`    🔗 相关关键词:`)
      keywordReport.relatedKeywords.forEach(kw => {
        console.log(`      - "${kw.keyword}": ${kw.density}% (${kw.occurrences}次)`)
      })
    }
    
    // 关键词密度评估
    let densityStatus = '✅ 适中'
    if (keywordReport.density < 0.5) {
      densityStatus = '⚠️  过低'
    } else if (keywordReport.density > 3) {
      densityStatus = '❌ 过高'
    }
    console.log(`    📊 密度评估: ${densityStatus}`)
  }
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
SEO优化测试脚本

用法:
  npm run test-seo              # 运行完整SEO测试
  npm run test-seo -- --help    # 显示帮助信息

功能:
  1. 测试SEO优化功能
  2. 生成SEO性能报告
  3. 分析关键词密度
  4. 提供优化建议

输出:
  - 控制台显示测试结果
  - src/data/seo-performance-report.json    # 详细SEO报告
`)
}

// 检查命令行参数
const args = process.argv.slice(2)
if (args.includes('--help') || args.includes('-h')) {
  showHelp()
  process.exit(0)
}

// 运行主程序
main().catch(error => {
  console.error('❌ 脚本执行失败:', error)
  process.exit(1)
})
