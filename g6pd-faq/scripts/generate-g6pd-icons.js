#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 图标配置
const iconSizes = [
  { size: 16, name: 'favicon-16x16.png', simplified: true },
  { size: 32, name: 'favicon-32x32.png', simplified: true },
  { size: 48, name: 'favicon-48x48.png', simplified: false },
  { size: 180, name: 'apple-touch-icon.png', simplified: false },
  { size: 192, name: 'android-chrome-192x192.png', simplified: false },
  { size: 512, name: 'android-chrome-512x512.png', simplified: false }
];

// 创建简化版SVG（用于小尺寸）
function createSimplifiedSVG(size) {
  return `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bloodGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#EF4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DC2626;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 血滴形状 -->
  <path d="M ${size/2} ${size*0.15} 
           C ${size/2} ${size*0.15}, ${size*0.75} ${size*0.35}, ${size*0.85} ${size*0.55}
           C ${size*0.9} ${size*0.65}, ${size*0.9} ${size*0.75}, ${size*0.85} ${size*0.85}
           C ${size*0.75} ${size*0.95}, ${size*0.65} ${size*0.98}, ${size/2} ${size*0.98}
           C ${size*0.35} ${size*0.98}, ${size*0.25} ${size*0.95}, ${size*0.15} ${size*0.85}
           C ${size*0.1} ${size*0.75}, ${size*0.1} ${size*0.65}, ${size*0.15} ${size*0.55}
           C ${size*0.25} ${size*0.35}, ${size/2} ${size*0.15}, ${size/2} ${size*0.15} Z" 
        fill="url(#bloodGradient)"/>
</svg>`;
}

// 创建完整版SVG（用于大尺寸）
function createFullSVG(size) {
  const fontSize = Math.max(12, size * 0.12);
  const crossSize = size * 0.08;
  const crossPos = size * 0.85;
  
  return `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bloodGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#EF4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DC2626;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="${size/2}" cy="${size/2}" r="${size*0.45}" fill="#FFFFFF" stroke="#E5E7EB" stroke-width="${Math.max(1, size*0.008)}"/>
  
  <!-- 血滴主体 -->
  <path d="M ${size/2} ${size*0.2} 
           C ${size/2} ${size*0.2}, ${size*0.7} ${size*0.35}, ${size*0.8} ${size*0.55}
           C ${size*0.85} ${size*0.65}, ${size*0.85} ${size*0.75}, ${size*0.8} ${size*0.85}
           C ${size*0.7} ${size*0.95}, ${size*0.6} ${size*0.98}, ${size/2} ${size*0.98}
           C ${size*0.4} ${size*0.98}, ${size*0.3} ${size*0.95}, ${size*0.2} ${size*0.85}
           C ${size*0.15} ${size*0.75}, ${size*0.15} ${size*0.65}, ${size*0.2} ${size*0.55}
           C ${size*0.3} ${size*0.35}, ${size/2} ${size*0.2}, ${size/2} ${size*0.2} Z" 
        fill="url(#bloodGradient)"/>
  
  <!-- 血滴高光 -->
  <ellipse cx="${size*0.42}" cy="${size*0.4}" rx="${size*0.08}" ry="${size*0.12}" fill="#FFFFFF" opacity="0.3"/>
  
  <!-- G6PD 文字 -->
  <text x="${size/2}" y="${size*0.65}" 
        font-family="Arial, sans-serif" 
        font-size="${fontSize}" 
        font-weight="bold" 
        text-anchor="middle" 
        fill="#FFFFFF">G6PD</text>
  
  <!-- 医疗十字 -->
  <g transform="translate(${crossPos}, ${crossPos})" opacity="0.6">
    <rect x="${-crossSize/2}" y="${-crossSize/6}" width="${crossSize}" height="${crossSize/3}" fill="#DC2626"/>
    <rect x="${-crossSize/6}" y="${-crossSize/2}" width="${crossSize/3}" height="${crossSize}" fill="#DC2626"/>
  </g>
</svg>`;
}

// 创建目录
const iconsDir = path.join(__dirname, '../public/icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

console.log('🎨 开始生成G6PD图标...\n');

// 生成SVG文件
iconSizes.forEach(({ size, name, simplified }) => {
  const svgContent = simplified ? createSimplifiedSVG(size) : createFullSVG(size);
  const svgPath = path.join(iconsDir, name.replace('.png', '.svg'));
  
  fs.writeFileSync(svgPath, svgContent);
  console.log(`✅ 生成 ${name.replace('.png', '.svg')} (${size}x${size})`);
});

// 生成manifest.json
const manifestPath = path.join(__dirname, '../public/manifest.json');
const manifest = {
  "name": "G6PD FAQ - 蚕豆病知识库",
  "short_name": "G6PD FAQ",
  "description": "专业的G6PD缺乏症（蚕豆病）知识库和FAQ",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#dc2626",
  "icons": [
    {
      "src": "/icons/android-chrome-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icons/android-chrome-512x512.png", 
      "sizes": "512x512",
      "type": "image/png"
    },
    {
      "src": "/icons/favicon-48x48.png",
      "sizes": "48x48", 
      "type": "image/png"
    }
  ]
};

fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
console.log('✅ 更新 manifest.json');

console.log('\n📝 注意事项:');
console.log('1. SVG文件已生成，需要手动转换为PNG格式');
console.log('2. 可以使用在线工具或设计软件进行转换');
console.log('3. 确保PNG文件质量和透明度正确');
console.log('4. 生成favicon.ico文件（包含16x16和32x32）');

console.log('\n🔧 建议的转换工具:');
console.log('- 在线: https://convertio.co/svg-png/');
console.log('- 命令行: npm install -g svg2png-cli');
console.log('- 设计软件: Figma, Adobe Illustrator');

console.log('\n✨ G6PD图标生成完成！');
