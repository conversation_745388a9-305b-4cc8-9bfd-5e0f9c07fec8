#!/usr/bin/env tsx

import { execSync } from 'child_process'
import fs from 'fs'
import path from 'path'

/**
 * 程序化SEO完整流程执行脚本
 * 按顺序执行所有任务
 */

interface TaskStep {
  id: number
  name: string
  description: string
  command: string
  required: boolean
  estimatedTime: string
}

const TASKS: TaskStep[] = [
  {
    id: 1,
    name: '关键词数据处理',
    description: '处理和清理长尾关键词数据',
    command: 'npm run process-keywords',
    required: true,
    estimatedTime: '30秒'
  },
  {
    id: 2,
    name: '内容自动生成',
    description: '基于关键词生成FAQ内容',
    command: 'npm run generate-content',
    required: true,
    estimatedTime: '2分钟'
  },
  {
    id: 3,
    name: '导航结构测试',
    description: '测试URL结构和内部链接',
    command: 'npm run test-navigation',
    required: false,
    estimatedTime: '30秒'
  },
  {
    id: 4,
    name: 'SEO优化测试',
    description: '测试SEO优化功能',
    command: 'npm run test-seo',
    required: false,
    estimatedTime: '1分钟'
  },
  {
    id: 5,
    name: '站点地图生成',
    description: '生成完整的站点地图系统',
    command: 'npm run generate-sitemap',
    required: true,
    estimatedTime: '30秒'
  },
  {
    id: 6,
    name: '综合测试验收',
    description: '运行完整的测试套件和性能监控',
    command: 'npm run test-all',
    required: true,
    estimatedTime: '2分钟'
  }
]

async function main() {
  const args = process.argv.slice(2)
  
  if (args.includes('--help') || args.includes('-h')) {
    showHelp()
    return
  }

  const skipOptional = args.includes('--skip-optional')
  const dryRun = args.includes('--dry-run')
  const verbose = args.includes('--verbose')

  console.log('🚀 程序化SEO完整流程执行')
  console.log('=' .repeat(60))
  console.log(`执行模式: ${dryRun ? '干运行' : '正常执行'}`)
  console.log(`可选任务: ${skipOptional ? '跳过' : '执行'}`)
  console.log('=' .repeat(60))

  const tasksToRun = skipOptional ? TASKS.filter(task => task.required) : TASKS
  const totalEstimatedTime = calculateTotalTime(tasksToRun)

  console.log(`📋 计划执行 ${tasksToRun.length} 个任务`)
  console.log(`⏱️  预计总时间: ${totalEstimatedTime}`)
  console.log()

  if (dryRun) {
    console.log('🔍 干运行模式 - 显示将要执行的任务:')
    tasksToRun.forEach(task => {
      console.log(`  ${task.id}. ${task.name} (${task.estimatedTime})`)
      console.log(`     ${task.description}`)
      console.log(`     命令: ${task.command}`)
      console.log()
    })
    return
  }

  // 检查前置条件
  console.log('🔍 检查前置条件...')
  await checkPrerequisites()

  // 执行任务
  const results: Array<{task: TaskStep, success: boolean, duration: number, error?: string}> = []
  let totalStartTime = Date.now()

  for (let i = 0; i < tasksToRun.length; i++) {
    const task = tasksToRun[i]
    console.log(`\n📋 任务 ${task.id}/${TASKS.length}: ${task.name}`)
    console.log(`📝 ${task.description}`)
    console.log(`⏱️  预计时间: ${task.estimatedTime}`)
    console.log(`🔧 执行命令: ${task.command}`)
    console.log('-'.repeat(50))

    const startTime = Date.now()
    let success = false
    let error: string | undefined

    try {
      if (verbose) {
        execSync(task.command, { stdio: 'inherit', cwd: process.cwd() })
      } else {
        const output = execSync(task.command, { cwd: process.cwd(), encoding: 'utf8' })
        console.log('✅ 任务执行成功')
        if (verbose) {
          console.log(output)
        }
      }
      success = true
    } catch (err) {
      error = err instanceof Error ? err.message : '未知错误'
      console.error(`❌ 任务执行失败: ${error}`)
      
      if (task.required) {
        console.error('💥 必需任务失败，停止执行')
        process.exit(1)
      } else {
        console.warn('⚠️  可选任务失败，继续执行')
      }
    }

    const duration = Date.now() - startTime
    results.push({ task, success, duration, error })

    console.log(`⏱️  实际耗时: ${Math.round(duration / 1000)}秒`)
    
    if (i < tasksToRun.length - 1) {
      console.log(`\n⏳ 准备执行下一个任务... (${i + 1}/${tasksToRun.length} 已完成)`)
    }
  }

  // 生成执行报告
  const totalDuration = Date.now() - totalStartTime
  await generateExecutionReport(results, totalDuration)

  // 显示最终结果
  console.log('\n' + '='.repeat(60))
  console.log('🎉 程序化SEO流程执行完成!')
  console.log('='.repeat(60))
  
  const successCount = results.filter(r => r.success).length
  const failureCount = results.filter(r => !r.success).length
  
  console.log(`📊 执行结果: ${successCount}/${results.length} 成功`)
  console.log(`⏱️  总耗时: ${Math.round(totalDuration / 1000)}秒`)
  
  if (failureCount > 0) {
    console.log(`⚠️  失败任务: ${failureCount} 个`)
    results.filter(r => !r.success).forEach(result => {
      console.log(`  - ${result.task.name}: ${result.error}`)
    })
  }

  // 显示后续步骤
  console.log('\n📋 后续步骤:')
  console.log('1. 检查生成的内容和站点地图')
  console.log('2. 部署到生产环境')
  console.log('3. 提交站点地图到搜索引擎')
  console.log('4. 监控SEO性能指标')
  console.log('5. 定期运行性能测试')

  console.log('\n📁 生成的文件位置:')
  console.log('- FAQ内容: src/data/generated-faqs/')
  console.log('- 站点地图: public/sitemap*.xml')
  console.log('- 性能报告: src/data/performance/')
  console.log('- 测试报告: src/data/test-reports/')

  if (failureCount === 0) {
    console.log('\n🎊 所有任务执行成功! 程序化SEO系统已就绪!')
  }
}

/**
 * 检查前置条件
 */
async function checkPrerequisites() {
  const checks = [
    {
      name: '检查Node.js版本',
      check: () => {
        const version = process.version
        const major = parseInt(version.slice(1).split('.')[0])
        if (major < 18) {
          throw new Error(`需要Node.js 18+，当前版本: ${version}`)
        }
        return `Node.js ${version}`
      }
    },
    {
      name: '检查依赖包',
      check: () => {
        const packagePath = path.join(process.cwd(), 'package.json')
        if (!fs.existsSync(packagePath)) {
          throw new Error('package.json不存在')
        }
        
        const nodeModulesPath = path.join(process.cwd(), 'node_modules')
        if (!fs.existsSync(nodeModulesPath)) {
          throw new Error('依赖包未安装，请运行 npm install')
        }
        
        return '依赖包已安装'
      }
    },
    {
      name: '检查数据目录',
      check: () => {
        const dataDir = path.join(process.cwd(), 'src/data')
        if (!fs.existsSync(dataDir)) {
          fs.mkdirSync(dataDir, { recursive: true })
        }
        
        const keywordsDir = path.join(dataDir, 'keywords')
        if (!fs.existsSync(keywordsDir)) {
          throw new Error('关键词数据目录不存在: src/data/keywords/')
        }
        
        return '数据目录检查通过'
      }
    },
    {
      name: '检查输出目录',
      check: () => {
        const publicDir = path.join(process.cwd(), 'public')
        if (!fs.existsSync(publicDir)) {
          fs.mkdirSync(publicDir, { recursive: true })
        }
        
        return '输出目录已准备'
      }
    }
  ]

  for (const check of checks) {
    try {
      const result = check.check()
      console.log(`✅ ${check.name}: ${result}`)
    } catch (error) {
      console.error(`❌ ${check.name}: ${error instanceof Error ? error.message : '检查失败'}`)
      process.exit(1)
    }
  }
  
  console.log('✅ 所有前置条件检查通过')
}

/**
 * 计算总预计时间
 */
function calculateTotalTime(tasks: TaskStep[]): string {
  // 简化的时间计算
  const totalMinutes = tasks.reduce((total, task) => {
    const timeStr = task.estimatedTime
    if (timeStr.includes('分钟')) {
      return total + parseInt(timeStr)
    } else if (timeStr.includes('秒')) {
      return total + parseInt(timeStr) / 60
    }
    return total + 1 // 默认1分钟
  }, 0)
  
  if (totalMinutes < 1) {
    return '不到1分钟'
  } else if (totalMinutes < 60) {
    return `约${Math.round(totalMinutes)}分钟`
  } else {
    const hours = Math.floor(totalMinutes / 60)
    const minutes = Math.round(totalMinutes % 60)
    return `约${hours}小时${minutes}分钟`
  }
}

/**
 * 生成执行报告
 */
async function generateExecutionReport(
  results: Array<{task: TaskStep, success: boolean, duration: number, error?: string}>,
  totalDuration: number
) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalTasks: results.length,
      successfulTasks: results.filter(r => r.success).length,
      failedTasks: results.filter(r => !r.success).length,
      totalDuration: totalDuration
    },
    tasks: results.map(result => ({
      id: result.task.id,
      name: result.task.name,
      description: result.task.description,
      command: result.task.command,
      success: result.success,
      duration: result.duration,
      error: result.error
    }))
  }

  const outputDir = path.join(process.cwd(), 'src/data/execution-reports')
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true })
  }

  const timestamp = new Date().toISOString().split('T')[0]
  const filename = `execution-report-${timestamp}.json`
  const filePath = path.join(outputDir, filename)

  fs.writeFileSync(filePath, JSON.stringify(report, null, 2), 'utf8')
  
  // 保存最新报告
  const latestPath = path.join(outputDir, 'latest-execution-report.json')
  fs.writeFileSync(latestPath, JSON.stringify(report, null, 2), 'utf8')

  console.log(`📄 执行报告已保存到: ${filePath}`)
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
程序化SEO完整流程执行脚本

用法:
  npm run run-all-tasks                    # 执行所有任务
  npm run run-all-tasks -- --skip-optional # 跳过可选任务
  npm run run-all-tasks -- --dry-run       # 干运行模式
  npm run run-all-tasks -- --verbose       # 详细输出
  npm run run-all-tasks -- --help          # 显示帮助

选项:
  --skip-optional    跳过可选任务，只执行必需任务
  --dry-run          干运行模式，显示将要执行的任务但不实际执行
  --verbose          显示详细的执行输出
  --help             显示帮助信息

任务列表:
${TASKS.map(task => `  ${task.id}. ${task.name} ${task.required ? '(必需)' : '(可选)'}`).join('\n')}

执行顺序:
  1. 处理关键词数据
  2. 生成FAQ内容
  3. 测试导航结构 (可选)
  4. 测试SEO优化 (可选)
  5. 生成站点地图
  6. 运行综合测试

注意:
  - 必需任务失败会停止整个流程
  - 可选任务失败会显示警告但继续执行
  - 建议在首次运行时使用 --dry-run 查看执行计划
`)
}

// 运行主程序
main().catch(error => {
  console.error('❌ 流程执行失败:', error)
  process.exit(1)
})
