#!/usr/bin/env tsx

import { keywordProcessor } from '../src/lib/keyword-processor'
import { programmaticSEOManager } from '../src/lib/programmatic-seo'
import { contentGenerator } from '../src/lib/content-generator'
import path from 'path'

/**
 * 长尾关键词处理脚本
 * 用于批量处理和生成FAQ内容
 */

async function main() {
  console.log('🚀 开始处理长尾关键词数据...')
  
  try {
    // 1. 初始化内容生成器
    console.log('📝 初始化内容模板...')
    await contentGenerator.initializeTemplates()
    
    // 2. 加载数据源
    console.log('📊 加载关键词数据源...')
    const dataSources = await programmaticSEOManager.loadKeywordDataSources()
    
    for (const source of dataSources) {
      console.log(`\n处理数据源: ${source.name}`)
      
      try {
        // 3. 处理关键词文件
        const keywords = await keywordProcessor.processKeywordFile(
          source.filePath, 
          source.id
        )
        
        // 4. 显示统计信息
        const stats = keywordProcessor.getKeywordStats(source.id)
        console.log(`📈 统计信息:`)
        console.log(`  - 总关键词数: ${stats.total}`)
        console.log(`  - 按意图分布:`, stats.byIntent)
        console.log(`  - 按难度分布:`, stats.byDifficulty)
        console.log(`  - 前5个高搜索量关键词:`)
        stats.topKeywords.slice(0, 5).forEach((kw, index) => {
          console.log(`    ${index + 1}. ${kw.keyword} (${kw.searchVolume.toLocaleString()})`)
        })
        
        // 5. 生成示例FAQ内容
        console.log(`\n🔧 生成示例FAQ内容...`)
        await generateSampleFAQs(source.id, keywords.slice(0, 3))
        
      } catch (error) {
        console.error(`❌ 处理数据源 ${source.name} 时出错:`, error)
      }
    }
    
    console.log('\n✅ 关键词处理完成!')
    
  } catch (error) {
    console.error('❌ 处理过程中出现错误:', error)
    process.exit(1)
  }
}

/**
 * 生成示例FAQ内容
 */
async function generateSampleFAQs(sourceId: string, keywords: any[]) {
  const templateMap: Record<string, string> = {
    'chinese-medicine-keywords': 'chinese-medicine-faq',
    'oral-solution-keywords': 'oral-solution-faq'
  }
  
  const templateId = templateMap[sourceId]
  if (!templateId) {
    console.log(`⚠️  未找到对应的模板: ${sourceId}`)
    return
  }
  
  for (const keyword of keywords) {
    try {
      console.log(`  生成FAQ: ${keyword.keyword}`)
      const faq = await contentGenerator.generateFAQFromKeyword(keyword, templateId)
      
      // 保存生成的FAQ到文件
      await saveFAQToFile(faq, sourceId)
      
      console.log(`    ✓ 质量评分: ${faq.qualityScore}/100`)
      console.log(`    ✓ SEO评分: ${faq.seo.readabilityScore}/100`)
      
    } catch (error) {
      console.error(`    ❌ 生成FAQ失败: ${keyword.keyword}`, error)
    }
  }
}

/**
 * 保存FAQ到文件
 */
async function saveFAQToFile(faq: any, sourceId: string) {
  const fs = await import('fs')
  const outputDir = path.join(process.cwd(), 'src/data/generated-faqs', sourceId)
  
  // 确保目录存在
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true })
  }
  
  const fileName = `${faq.slug}.json`
  const filePath = path.join(outputDir, fileName)
  
  fs.writeFileSync(filePath, JSON.stringify(faq, null, 2), 'utf8')
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
长尾关键词处理脚本

用法:
  npm run process-keywords              # 处理所有关键词文件
  npm run process-keywords -- --help    # 显示帮助信息

功能:
  1. 解析长尾关键词文件
  2. 数据清洗和分类
  3. 生成关键词变体和相关术语
  4. 自动生成FAQ内容
  5. 保存处理结果

输出:
  - src/data/processed-keywords/        # 处理后的关键词数据
  - src/data/generated-faqs/           # 生成的FAQ内容
`)
}

// 检查命令行参数
const args = process.argv.slice(2)
if (args.includes('--help') || args.includes('-h')) {
  showHelp()
  process.exit(0)
}

// 运行主程序
main().catch(error => {
  console.error('❌ 脚本执行失败:', error)
  process.exit(1)
})
