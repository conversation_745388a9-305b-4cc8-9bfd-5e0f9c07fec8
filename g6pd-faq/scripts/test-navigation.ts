#!/usr/bin/env tsx

import { urlStructureManager } from '../src/lib/url-structure'
import { internalLinkingManager } from '../src/lib/internal-linking'
import { FAQ } from '../src/lib/types'
import fs from 'fs'
import path from 'path'

/**
 * 导航和URL结构测试脚本
 */

async function main() {
  console.log('🧭 开始测试导航和URL结构...')
  
  try {
    // 1. 加载生成的FAQ数据
    const faqs = await loadGeneratedFAQs()
    console.log(`📚 加载了 ${faqs.length} 个FAQ`)
    
    // 2. 初始化内部链接索引
    internalLinkingManager.initializeIndex(faqs)
    
    // 3. 测试URL结构生成
    console.log('\n🔗 测试URL结构生成...')
    await testURLStructure(faqs.slice(0, 3))
    
    // 4. 测试分类导航
    console.log('\n📂 测试分类导航...')
    await testCategoryNavigation(faqs)
    
    // 5. 测试内部链接策略
    console.log('\n🔗 测试内部链接策略...')
    await testInternalLinking(faqs.slice(0, 2))
    
    // 6. 生成站点地图预览
    console.log('\n🗺️  生成站点地图预览...')
    await generateSitemapPreview(faqs)
    
    console.log('\n✅ 导航测试完成!')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
    process.exit(1)
  }
}

/**
 * 加载生成的FAQ数据
 */
async function loadGeneratedFAQs(): Promise<FAQ[]> {
  const faqs: FAQ[] = []
  const generatedDir = path.join(process.cwd(), 'src/data/generated-faqs')
  
  if (!fs.existsSync(generatedDir)) {
    console.log('⚠️  生成的FAQ目录不存在')
    return faqs
  }
  
  const sources = fs.readdirSync(generatedDir)
  
  for (const source of sources) {
    const sourcePath = path.join(generatedDir, source)
    if (fs.statSync(sourcePath).isDirectory()) {
      const files = fs.readdirSync(sourcePath)
        .filter(file => file.endsWith('.json') && file !== 'index.json')
      
      for (const file of files) {
        try {
          const filePath = path.join(sourcePath, file)
          const faqData = JSON.parse(fs.readFileSync(filePath, 'utf8'))
          faqs.push(faqData)
        } catch (error) {
          console.warn(`⚠️  无法加载FAQ文件: ${file}`)
        }
      }
    }
  }
  
  return faqs
}

/**
 * 测试URL结构生成
 */
async function testURLStructure(faqs: FAQ[]) {
  for (const faq of faqs) {
    console.log(`\n📄 FAQ: ${faq.title}`)
    
    // 中文URL结构
    const zhStructure = urlStructureManager.generateFAQURLStructure(faq, 'zh')
    console.log(`  🇨🇳 中文URL: ${zhStructure.canonicalUrl}`)
    console.log(`  📍 路径: ${zhStructure.path}`)
    console.log(`  🍞 面包屑:`)
    zhStructure.breadcrumbs.forEach(crumb => {
      console.log(`    ${crumb.position}. ${crumb.name} (${crumb.url})`)
    })
    
    // 英文URL结构
    const enStructure = urlStructureManager.generateFAQURLStructure(faq, 'en')
    console.log(`  🇺🇸 英文URL: ${enStructure.canonicalUrl}`)
    
    // 结构化数据
    console.log(`  📊 结构化数据类型: ${zhStructure.structuredData['@type']}`)
  }
}

/**
 * 测试分类导航
 */
async function testCategoryNavigation(faqs: FAQ[]) {
  const zhNavigation = urlStructureManager.generateCategoryNavigation(faqs, 'zh')
  const enNavigation = urlStructureManager.generateCategoryNavigation(faqs, 'en')
  
  console.log('🇨🇳 中文导航结构:')
  zhNavigation.forEach(category => {
    console.log(`  📁 ${category.title} (${category.count} 个FAQ) - ${category.url}`)
    if (category.children && category.children.length > 0) {
      category.children.forEach(subcategory => {
        console.log(`    📂 ${subcategory.title} (${subcategory.count} 个FAQ) - ${subcategory.url}`)
      })
    }
  })
  
  console.log('\n🇺🇸 英文导航结构:')
  enNavigation.forEach(category => {
    console.log(`  📁 ${category.title} (${category.count} FAQs) - ${category.url}`)
    if (category.children && category.children.length > 0) {
      category.children.forEach(subcategory => {
        console.log(`    📂 ${subcategory.title} (${subcategory.count} FAQs) - ${subcategory.url}`)
      })
    }
  })
}

/**
 * 测试内部链接策略
 */
async function testInternalLinking(faqs: FAQ[]) {
  for (const faq of faqs) {
    console.log(`\n🔗 FAQ: ${faq.title}`)
    
    const strategy = internalLinkingManager.generateLinkingStrategy(faq, 'zh')
    
    console.log(`  📝 上下文链接 (${strategy.contextualLinks.length} 个):`)
    strategy.contextualLinks.forEach(link => {
      console.log(`    - ${link.text} → ${link.url}`)
    })
    
    console.log(`  🔗 相关FAQ (${strategy.relatedFAQs.length} 个):`)
    strategy.relatedFAQs.forEach(link => {
      console.log(`    - ${link.text} → ${link.url}`)
    })
    
    console.log(`  📂 分类链接 (${strategy.categoryLinks.length} 个):`)
    strategy.categoryLinks.forEach(link => {
      console.log(`    - ${link.text} → ${link.url}`)
    })
    
    console.log(`  🏷️  标签链接 (${strategy.tagLinks.length} 个):`)
    strategy.tagLinks.forEach(link => {
      console.log(`    - ${link.text} → ${link.url}`)
    })
    
    // 测试内容中插入链接
    const originalContent = faq.answer.substring(0, 200) + '...'
    const linkedContent = internalLinkingManager.insertLinksInContent(originalContent, faq, 'zh')
    
    if (linkedContent !== originalContent) {
      console.log(`  ✨ 内容链接插入成功`)
      const report = internalLinkingManager.generateLinkDensityReport(linkedContent)
      console.log(`    📊 链接密度: ${report.linkDensity}% (${report.totalLinks}/${report.totalWords})`)
      console.log(`    💡 建议: ${report.recommendation}`)
    } else {
      console.log(`  ⚠️  未找到可插入的链接`)
    }
  }
}

/**
 * 生成站点地图预览
 */
async function generateSitemapPreview(faqs: FAQ[]) {
  const sitemapUrls = urlStructureManager.generateSitemapUrls(faqs)
  
  console.log(`📊 站点地图统计:`)
  console.log(`  - 总URL数: ${sitemapUrls.length}`)
  
  // 按优先级分组
  const priorityGroups = sitemapUrls.reduce((groups, url) => {
    const priority = url.priority.toString()
    if (!groups[priority]) {
      groups[priority] = 0
    }
    groups[priority]++
    return groups
  }, {} as Record<string, number>)
  
  console.log(`  - 优先级分布:`)
  Object.entries(priorityGroups)
    .sort((a, b) => parseFloat(b[0]) - parseFloat(a[0]))
    .forEach(([priority, count]) => {
      console.log(`    ${priority}: ${count} 个URL`)
    })
  
  // 显示前10个高优先级URL
  console.log(`\n🔝 前10个高优先级URL:`)
  sitemapUrls
    .sort((a, b) => b.priority - a.priority)
    .slice(0, 10)
    .forEach((url, index) => {
      console.log(`  ${index + 1}. ${url.url} (优先级: ${url.priority})`)
    })
  
  // 保存站点地图预览
  const sitemapPreview = {
    generatedAt: new Date().toISOString(),
    totalUrls: sitemapUrls.length,
    priorityDistribution: priorityGroups,
    urls: sitemapUrls.slice(0, 20) // 只保存前20个作为预览
  }
  
  const outputPath = path.join(process.cwd(), 'src/data/sitemap-preview.json')
  fs.writeFileSync(outputPath, JSON.stringify(sitemapPreview, null, 2), 'utf8')
  console.log(`💾 站点地图预览已保存到: ${outputPath}`)
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
导航和URL结构测试脚本

用法:
  npm run test-navigation              # 运行完整测试
  npm run test-navigation -- --help    # 显示帮助信息

功能:
  1. 测试URL结构生成
  2. 测试分类导航结构
  3. 测试内部链接策略
  4. 生成站点地图预览

输出:
  - 控制台显示测试结果
  - src/data/sitemap-preview.json     # 站点地图预览
`)
}

// 检查命令行参数
const args = process.argv.slice(2)
if (args.includes('--help') || args.includes('-h')) {
  showHelp()
  process.exit(0)
}

// 运行主程序
main().catch(error => {
  console.error('❌ 脚本执行失败:', error)
  process.exit(1)
})
