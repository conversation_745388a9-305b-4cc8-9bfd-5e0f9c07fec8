#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const crypto = require('crypto')

// 配置
const SITE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd.site'
const PUBLIC_DIR = path.join(process.cwd(), 'public')
const LOCALES = ['zh', 'en']

// 分层sitemap配置
const SITEMAP_CONFIG = {
  static: {
    filename: 'sitemap_static.xml',
    changefreq: 'weekly',
    priority: 0.8,
    urls: [
      { path: '', priority: 1.0 }, // 首页
      { path: '/[locale]', priority: 0.9 },
      { path: '/[locale]/about', priority: 0.6 },
      { path: '/[locale]/contact', priority: 0.6 },
      { path: '/[locale]/privacy', priority: 0.3 },
      { path: '/[locale]/terms', priority: 0.3 },
      { path: '/[locale]/search', priority: 0.7 }
    ]
  },
  categories: {
    filename: 'sitemap_categories.xml',
    changefreq: 'daily',
    priority: 0.7,
    urls: [
      { path: '/[locale]/faq', priority: 0.8 },
      { path: '/[locale]/faq/medications', priority: 0.7 },
      { path: '/[locale]/faq/medications/chinese-medicine', priority: 0.7 },
      { path: '/[locale]/faq/medications/oral-solutions', priority: 0.7 },
      { path: '/[locale]/faq/symptoms', priority: 0.7 },
      { path: '/[locale]/faq/diet', priority: 0.7 },
      { path: '/[locale]/faq/treatment', priority: 0.7 },
      { path: '/[locale]/medications', priority: 0.7 },
      { path: '/[locale]/medications/chinese-medicine', priority: 0.7 },
      { path: '/[locale]/medications/oral-solutions', priority: 0.7 },
      { path: '/[locale]/medications/western-medicine', priority: 0.7 }
    ]
  },
  programmatic: {
    filename: 'sitemap_programmatic.xml',
    changefreq: 'monthly',
    priority: 0.9
  }
}

// 生成内容哈希
function generateContentHash(content) {
  return crypto.createHash('md5').update(content).digest('hex')
}

// 检查文件是否需要更新
function shouldUpdateSitemap(filePath, newContent) {
  if (!fs.existsSync(filePath)) {
    console.log(`📝 Creating new sitemap: ${path.basename(filePath)}`)
    return true
  }
  
  try {
    const existingContent = fs.readFileSync(filePath, 'utf8')
    const existingHash = generateContentHash(existingContent)
    const newHash = generateContentHash(newContent)
    
    if (existingHash !== newHash) {
      console.log(`🔄 Content changed, updating: ${path.basename(filePath)}`)
      return true
    } else {
      console.log(`✅ No changes detected, skipping: ${path.basename(filePath)}`)
      return false
    }
  } catch (error) {
    console.warn(`⚠️ Error checking sitemap file ${filePath}:`, error)
    return true
  }
}

// 生成XML sitemap内容
function generateSitemapXML(urls) {
  const urlEntries = urls.map(url => {
    const lastmod = url.lastmod || new Date().toISOString()
    const changefreq = url.changefreq || 'monthly'
    const priority = url.priority || 0.7
    
    return `<url>
  <loc>${url.loc}</loc>
  <lastmod>${lastmod}</lastmod>
  <changefreq>${changefreq}</changefreq>
  <priority>${priority}</priority>
</url>`
  }).join('\n')

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlEntries}
</urlset>`
}

// 生成sitemap索引文件
function generateSitemapIndex(sitemaps) {
  const sitemapEntries = sitemaps.map(sitemap => {
    return `<sitemap>
  <loc>${SITE_URL}/${sitemap.filename}</loc>
  <lastmod>${sitemap.lastmod}</lastmod>
</sitemap>`
  }).join('\n')

  return `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapEntries}
</sitemapindex>`
}

// 加载程序化SEO生成的FAQ内容
function loadGeneratedFAQs() {
  const faqs = []
  const generatedFaqsPath = path.join(process.cwd(), 'src/data/generated-faqs')
  
  if (!fs.existsSync(generatedFaqsPath)) {
    console.warn('⚠️ Generated FAQs directory not found:', generatedFaqsPath)
    return faqs
  }

  try {
    for (const subDir of fs.readdirSync(generatedFaqsPath)) {
      const subDirPath = path.join(generatedFaqsPath, subDir)
      if (fs.statSync(subDirPath).isDirectory()) {
        for (const file of fs.readdirSync(subDirPath).filter(f => f.endsWith('.json') && f !== 'index.json')) {
          try {
            const filePath = path.join(subDirPath, file)
            const faqData = JSON.parse(fs.readFileSync(filePath, 'utf8'))
            faqs.push(faqData)
          } catch (error) {
            console.warn(`⚠️ Failed to load FAQ file: ${file}`, error)
          }
        }
      }
    }
  } catch (error) {
    console.warn('⚠️ Error loading generated FAQs:', error)
  }

  console.log(`📊 Loaded ${faqs.length} programmatic SEO FAQs`)
  return faqs
}

// 生成静态页面sitemap
function generateStaticSitemap() {
  const urls = []
  // 使用固定的日期作为静态页面的lastmod，避免不必要的更新
  const staticLastmod = '2025-07-03T00:00:00.000Z'

  SITEMAP_CONFIG.static.urls.forEach(urlConfig => {
    if (urlConfig.path.includes('[locale]')) {
      // 为每个语言生成URL
      LOCALES.forEach(locale => {
        const path = urlConfig.path.replace('[locale]', locale)
        urls.push({
          loc: `${SITE_URL}${path}`,
          changefreq: SITEMAP_CONFIG.static.changefreq,
          priority: urlConfig.priority || SITEMAP_CONFIG.static.priority,
          lastmod: staticLastmod
        })
      })
    } else {
      // 根路径
      urls.push({
        loc: `${SITE_URL}${urlConfig.path}`,
        changefreq: SITEMAP_CONFIG.static.changefreq,
        priority: urlConfig.priority || SITEMAP_CONFIG.static.priority,
        lastmod: staticLastmod
      })
    }
  })

  return generateSitemapXML(urls)
}

// 生成分类页面sitemap
function generateCategoriesSitemap() {
  const urls = []
  // 使用固定的日期作为分类页面的lastmod，避免不必要的更新
  const categoriesLastmod = '2025-07-03T00:00:00.000Z'

  SITEMAP_CONFIG.categories.urls.forEach(urlConfig => {
    LOCALES.forEach(locale => {
      const path = urlConfig.path.replace('[locale]', locale)
      urls.push({
        loc: `${SITE_URL}${path}`,
        changefreq: SITEMAP_CONFIG.categories.changefreq,
        priority: urlConfig.priority || SITEMAP_CONFIG.categories.priority,
        lastmod: categoriesLastmod
      })
    })
  })

  return generateSitemapXML(urls)
}

// 生成程序化SEO sitemap
function generateProgrammaticSitemap() {
  const generatedFaqs = loadGeneratedFAQs()
  const urls = []

  generatedFaqs.forEach(faq => {
    // 过滤无效的slug
    if (!faq.slug || faq.slug === '-' || faq.slug.trim() === '') {
      console.warn(`⚠️ Skipping FAQ with invalid slug: ${faq.slug || 'undefined'} (ID: ${faq.id})`)
      return
    }

    const locale = faq.locale || 'zh'
    const category = faq.category === 'medications' ? '/medications' : `/${faq.category}`
    const subcategory = faq.subcategory ? `/${faq.subcategory}` : ''
    const path = `/${locale}/faq${category}${subcategory}/${encodeURIComponent(faq.slug)}`

    urls.push({
      loc: `${SITE_URL}${path}`,
      changefreq: SITEMAP_CONFIG.programmatic.changefreq,
      priority: faq.priority ? Math.min(faq.priority / 100, 0.9) : SITEMAP_CONFIG.programmatic.priority,
      lastmod: faq.lastUpdated ? new Date(faq.lastUpdated).toISOString() : new Date().toISOString()
    })
  })

  console.log(`📊 Generated ${urls.length} valid programmatic SEO URLs`)
  return generateSitemapXML(urls)
}

// 主函数
async function generateHierarchicalSitemap() {
  console.log('🚀 Starting hierarchical sitemap generation...')
  
  // 确保public目录存在
  if (!fs.existsSync(PUBLIC_DIR)) {
    fs.mkdirSync(PUBLIC_DIR, { recursive: true })
  }
  
  const sitemaps = []
  let totalUpdated = 0
  
  // 生成静态页面sitemap
  const staticContent = generateStaticSitemap()
  const staticPath = path.join(PUBLIC_DIR, SITEMAP_CONFIG.static.filename)
  if (shouldUpdateSitemap(staticPath, staticContent)) {
    fs.writeFileSync(staticPath, staticContent)
    totalUpdated++
  }
  sitemaps.push({
    filename: SITEMAP_CONFIG.static.filename,
    lastmod: '2025-07-03T00:00:00.000Z'
  })

  // 生成分类页面sitemap
  const categoriesContent = generateCategoriesSitemap()
  const categoriesPath = path.join(PUBLIC_DIR, SITEMAP_CONFIG.categories.filename)
  if (shouldUpdateSitemap(categoriesPath, categoriesContent)) {
    fs.writeFileSync(categoriesPath, categoriesContent)
    totalUpdated++
  }
  sitemaps.push({
    filename: SITEMAP_CONFIG.categories.filename,
    lastmod: '2025-07-03T00:00:00.000Z'
  })

  // 生成程序化SEO sitemap
  const programmaticContent = generateProgrammaticSitemap()
  const programmaticPath = path.join(PUBLIC_DIR, SITEMAP_CONFIG.programmatic.filename)
  if (shouldUpdateSitemap(programmaticPath, programmaticContent)) {
    fs.writeFileSync(programmaticPath, programmaticContent)
    totalUpdated++
  }
  sitemaps.push({
    filename: SITEMAP_CONFIG.programmatic.filename,
    lastmod: '2025-07-03T00:00:00.000Z'
  })
  
  // 生成主sitemap索引文件
  const indexContent = generateSitemapIndex(sitemaps)
  const indexPath = path.join(PUBLIC_DIR, 'sitemap.xml')
  if (shouldUpdateSitemap(indexPath, indexContent)) {
    fs.writeFileSync(indexPath, indexContent)
    totalUpdated++
  }
  
  console.log(`✅ Hierarchical sitemap generation completed!`)
  console.log(`📊 Total files updated: ${totalUpdated}/4`)
  console.log(`📁 Generated sitemaps:`)
  sitemaps.forEach(sitemap => {
    console.log(`   - ${sitemap.filename}`)
  })
  console.log(`   - sitemap.xml (index)`)
}

// 运行脚本
if (require.main === module) {
  generateHierarchicalSitemap().catch(console.error)
}

module.exports = { generateHierarchicalSitemap }
