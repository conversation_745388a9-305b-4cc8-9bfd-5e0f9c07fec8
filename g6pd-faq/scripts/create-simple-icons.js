#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 创建简单的PNG数据（1x1像素红色PNG的base64）
const createSimplePNG = (size) => {
  // 这是一个1x1红色像素的PNG文件的base64编码
  // 我们将用它作为临时占位符
  const redPixelPNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
  return Buffer.from(redPixelPNG, 'base64');
};

// 创建favicon.ico文件
const createFaviconICO = () => {
  // 这是一个简单的16x16红色favicon的ICO格式
  const faviconData = Buffer.from([
    0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x10, 0x10, 0x00, 0x00, 0x01, 0x00, 0x20, 0x00, 0x68, 0x04,
    0x00, 0x00, 0x16, 0x00, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x20, 0x00,
    0x00, 0x00, 0x01, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  ]);
  
  // 填充红色像素数据
  const pixelData = Buffer.alloc(16 * 16 * 4); // 16x16 RGBA
  for (let i = 0; i < 16 * 16; i++) {
    const offset = i * 4;
    pixelData[offset] = 0x26;     // B
    pixelData[offset + 1] = 0x26; // G  
    pixelData[offset + 2] = 0xDC; // R
    pixelData[offset + 3] = 0xFF; // A
  }
  
  return Buffer.concat([faviconData, pixelData]);
};

const iconsDir = path.join(__dirname, '../public/icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

console.log('🎨 创建临时PNG图标文件...\n');

// 创建PNG文件
const iconSizes = [
  { size: 16, name: 'favicon-16x16.png' },
  { size: 32, name: 'favicon-32x32.png' },
  { size: 48, name: 'favicon-48x48.png' },
  { size: 180, name: 'apple-touch-icon.png' },
  { size: 192, name: 'android-chrome-192x192.png' },
  { size: 512, name: 'android-chrome-512x512.png' }
];

iconSizes.forEach(({ size, name }) => {
  const pngData = createSimplePNG(size);
  const filePath = path.join(iconsDir, name);
  fs.writeFileSync(filePath, pngData);
  console.log(`✅ 创建 ${name} (${size}x${size})`);
});

// 创建favicon.ico
const faviconPath = path.join(__dirname, '../public/favicon.ico');
const faviconData = createFaviconICO();
fs.writeFileSync(faviconPath, faviconData);
console.log('✅ 创建 favicon.ico');

console.log('\n📝 注意: 这些是临时的简单图标文件');
console.log('🔧 建议使用专业工具创建高质量的PNG图标:');
console.log('- 在线工具: https://favicon.io/favicon-converter/');
console.log('- 设计软件: Figma, Canva, Adobe Illustrator');
console.log('- 或使用之前生成的SVG文件进行转换');

console.log('\n✨ 临时图标文件创建完成！');
