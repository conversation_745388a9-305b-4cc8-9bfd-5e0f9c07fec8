#!/usr/bin/env tsx

import { sitemapGenerator } from '../src/lib/sitemap-generator'
import { FAQ } from '../src/lib/types'
import fs from 'fs'
import path from 'path'

/**
 * 站点地图生成脚本
 */

interface ScriptOptions {
  outputDir?: string
  dryRun?: boolean
  verbose?: boolean
}

async function main() {
  const args = process.argv.slice(2)
  const options = parseArgs(args)

  if (args.includes('--help') || args.includes('-h')) {
    showHelp()
    return
  }

  console.log('🗺️  开始生成站点地图...')
  
  try {
    // 1. 加载FAQ数据
    const faqs = await loadGeneratedFAQs()
    console.log(`📚 加载了 ${faqs.length} 个FAQ`)
    
    if (faqs.length === 0) {
      console.log('⚠️  没有找到FAQ数据，请先运行内容生成')
      return
    }

    // 2. 配置站点地图生成器
    const config = {
      baseUrl: 'https://g6pd.site',
      outputDir: options.outputDir || './public',
      maxUrlsPerSitemap: 50000,
      defaultChangefreq: 'monthly' as const,
      defaultPriority: 0.5
    }

    const generator = sitemapGenerator
    
    if (options.dryRun) {
      console.log('🔍 干运行模式 - 不会生成实际文件')
      await performDryRun(faqs, config)
    } else {
      // 3. 生成站点地图
      console.log(`📁 输出目录: ${config.outputDir}`)
      const result = await generator.generateSitemaps(faqs)
      
      // 4. 生成robots.txt
      console.log('\n🤖 生成robots.txt...')
      const robotsFile = await generator.generateRobotsTxt()
      console.log(`✅ robots.txt已生成: ${robotsFile}`)
      
      // 5. 显示结果统计
      console.log('\n📊 生成结果统计:')
      console.log(`  - 主站点地图: ${result.mainSitemap}`)
      console.log(`  - FAQ站点地图: ${result.faqSitemaps.length} 个`)
      console.log(`  - 分类站点地图: ${result.categorySitemaps.length} 个`)
      console.log(`  - 站点地图索引: ${result.sitemapIndex}`)
      
      // 6. 验证生成的文件
      await validateGeneratedFiles(config.outputDir, result)
    }
    
    console.log('\n✅ 站点地图生成完成!')
    
  } catch (error) {
    console.error('❌ 生成失败:', error)
    process.exit(1)
  }
}

/**
 * 解析命令行参数
 */
function parseArgs(args: string[]): ScriptOptions {
  const options: ScriptOptions = {}
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i]
    
    if (arg === '--output-dir' || arg === '-o') {
      options.outputDir = args[++i]
    } else if (arg === '--dry-run') {
      options.dryRun = true
    } else if (arg === '--verbose' || arg === '-v') {
      options.verbose = true
    }
  }
  
  return options
}

/**
 * 加载生成的FAQ数据
 */
async function loadGeneratedFAQs(): Promise<FAQ[]> {
  const faqs: FAQ[] = []
  const generatedDir = path.join(process.cwd(), 'src/data/generated-faqs')
  
  if (!fs.existsSync(generatedDir)) {
    return faqs
  }
  
  const sources = fs.readdirSync(generatedDir)
  
  for (const source of sources) {
    const sourcePath = path.join(generatedDir, source)
    if (fs.statSync(sourcePath).isDirectory()) {
      const files = fs.readdirSync(sourcePath)
        .filter(file => file.endsWith('.json') && file !== 'index.json')
      
      for (const file of files) {
        try {
          const filePath = path.join(sourcePath, file)
          const faqData = JSON.parse(fs.readFileSync(filePath, 'utf8'))
          faqs.push(faqData)
        } catch (error) {
          console.warn(`⚠️  无法加载FAQ文件: ${file}`)
        }
      }
    }
  }
  
  return faqs
}

/**
 * 执行干运行
 */
async function performDryRun(faqs: FAQ[], config: any) {
  console.log('\n🔍 分析站点地图结构...')
  
  // 分析URL数量
  const totalUrls = faqs.length * 2 // 中英文各一份
  const staticUrls = 12 // 主页、FAQ首页、静态页面等
  const categoryUrls = calculateCategoryUrls(faqs)
  
  console.log(`📊 URL统计:`)
  console.log(`  - FAQ页面: ${totalUrls} 个`)
  console.log(`  - 静态页面: ${staticUrls} 个`)
  console.log(`  - 分类页面: ${categoryUrls} 个`)
  console.log(`  - 总计: ${totalUrls + staticUrls + categoryUrls} 个URL`)
  
  // 分析站点地图文件数量
  const maxUrlsPerSitemap = config.maxUrlsPerSitemap
  const faqSitemaps = Math.ceil(totalUrls / maxUrlsPerSitemap)
  const categories = getUniqueCategories(faqs)
  
  console.log(`\n📁 预计生成文件:`)
  console.log(`  - sitemap.xml (索引文件)`)
  console.log(`  - sitemap-main.xml (主站点地图)`)
  console.log(`  - sitemap-faq-zh-*.xml (${Math.ceil(faqs.length / maxUrlsPerSitemap)} 个中文FAQ站点地图)`)
  console.log(`  - sitemap-faq-en-*.xml (${Math.ceil(faqs.length / maxUrlsPerSitemap)} 个英文FAQ站点地图)`)
  console.log(`  - sitemap-category-*.xml (${categories.length} 个分类站点地图)`)
  console.log(`  - robots.txt`)
  
  // 分析分类分布
  console.log(`\n📂 分类分布:`)
  const categoryStats = getCategoryStats(faqs)
  Object.entries(categoryStats).forEach(([category, count]) => {
    console.log(`  - ${category}: ${count} 个FAQ`)
  })
}

/**
 * 计算分类URL数量
 */
function calculateCategoryUrls(faqs: FAQ[]): number {
  const categories = getUniqueCategories(faqs)
  const subcategories = getUniqueSubcategories(faqs)
  
  // 每个分类和子分类都有中英文版本
  return (categories.length + subcategories.length) * 2
}

/**
 * 获取唯一分类
 */
function getUniqueCategories(faqs: FAQ[]): string[] {
  const categories = new Set<string>()
  faqs.forEach(faq => {
    if (faq.category) {
      categories.add(faq.category)
    }
  })
  return Array.from(categories)
}

/**
 * 获取唯一子分类
 */
function getUniqueSubcategories(faqs: FAQ[]): string[] {
  const subcategories = new Set<string>()
  faqs.forEach(faq => {
    if (faq.subcategory) {
      subcategories.add(faq.subcategory)
    }
  })
  return Array.from(subcategories)
}

/**
 * 获取分类统计
 */
function getCategoryStats(faqs: FAQ[]): Record<string, number> {
  const stats: Record<string, number> = {}
  faqs.forEach(faq => {
    const category = faq.category || 'general'
    stats[category] = (stats[category] || 0) + 1
  })
  return stats
}

/**
 * 验证生成的文件
 */
async function validateGeneratedFiles(outputDir: string, result: any) {
  console.log('\n🔍 验证生成的文件...')
  
  const filesToCheck = [
    result.mainSitemap,
    ...result.faqSitemaps,
    ...result.categorySitemaps,
    result.sitemapIndex,
    'robots.txt'
  ]
  
  let validFiles = 0
  let totalSize = 0
  
  for (const filename of filesToCheck) {
    const filePath = path.join(outputDir, filename)
    
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath)
      const sizeKB = Math.round(stats.size / 1024)
      totalSize += stats.size
      validFiles++
      
      console.log(`  ✅ ${filename} (${sizeKB} KB)`)
      
      // 验证XML格式
      if (filename.endsWith('.xml')) {
        try {
          const content = fs.readFileSync(filePath, 'utf8')
          if (!content.includes('<?xml version="1.0"')) {
            console.log(`    ⚠️  XML格式可能有问题`)
          }
        } catch (error) {
          console.log(`    ❌ 无法读取文件内容`)
        }
      }
    } else {
      console.log(`  ❌ ${filename} (文件不存在)`)
    }
  }
  
  console.log(`\n📊 验证结果:`)
  console.log(`  - 有效文件: ${validFiles}/${filesToCheck.length}`)
  console.log(`  - 总大小: ${Math.round(totalSize / 1024)} KB`)
  
  if (validFiles === filesToCheck.length) {
    console.log(`  ✅ 所有文件生成成功`)
  } else {
    console.log(`  ⚠️  部分文件生成失败`)
  }
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
站点地图生成脚本

用法:
  npm run generate-sitemap                    # 生成站点地图
  npm run generate-sitemap -- --dry-run       # 干运行模式（不生成文件）
  npm run generate-sitemap -- --output-dir ./dist  # 指定输出目录
  npm run generate-sitemap -- --help          # 显示帮助信息

选项:
  -o, --output-dir <dir>    指定输出目录 (默认: ./public)
  --dry-run                 干运行模式，分析但不生成文件
  -v, --verbose             详细输出
  -h, --help                显示帮助信息

功能:
  1. 生成主站点地图（静态页面）
  2. 生成FAQ站点地图（按语言分组）
  3. 生成分类站点地图
  4. 生成站点地图索引文件
  5. 生成robots.txt文件

输出文件:
  - sitemap.xml             # 站点地图索引
  - sitemap-main.xml        # 主站点地图
  - sitemap-faq-*.xml       # FAQ站点地图
  - sitemap-category-*.xml  # 分类站点地图
  - robots.txt              # 搜索引擎爬虫配置

注意:
  - 需要先运行内容生成脚本生成FAQ数据
  - 生成的文件将覆盖现有文件
  - 建议在部署前验证XML格式
`)
}

// 运行主程序
main().catch(error => {
  console.error('❌ 脚本执行失败:', error)
  process.exit(1)
})
