#!/usr/bin/env tsx

import { performanceMonitor } from '../src/lib/performance-monitor'
import { seoOptimizer } from '../src/lib/seo-optimizer'
import { internalLinkingManager } from '../src/lib/internal-linking'
import { urlStructureManager } from '../src/lib/url-structure'
import { sitemapGenerator } from '../src/lib/sitemap-generator'
import { FAQ } from '../src/lib/types'
import fs from 'fs'
import path from 'path'

/**
 * 程序化SEO综合测试脚本
 */

interface TestResult {
  name: string
  status: 'pass' | 'fail' | 'warning'
  message: string
  details?: any
  duration: number
}

interface TestSuite {
  name: string
  tests: TestResult[]
  totalTests: number
  passedTests: number
  failedTests: number
  warningTests: number
  duration: number
}

async function main() {
  console.log('🧪 开始程序化SEO综合测试...')
  console.log('=' .repeat(60))
  
  const startTime = Date.now()
  const testSuites: TestSuite[] = []

  try {
    // 1. 加载测试数据
    const faqs = await loadTestData()
    console.log(`📚 加载了 ${faqs.length} 个FAQ用于测试`)
    
    if (faqs.length === 0) {
      console.log('❌ 没有找到测试数据，请先运行内容生成')
      process.exit(1)
    }

    // 2. 运行各个测试套件
    testSuites.push(await runDataIntegrityTests(faqs))
    testSuites.push(await runSEOOptimizationTests(faqs))
    testSuites.push(await runInternalLinkingTests(faqs))
    testSuites.push(await runURLStructureTests(faqs))
    testSuites.push(await runSitemapTests(faqs))
    testSuites.push(await runPerformanceTests(faqs))
    testSuites.push(await runContentQualityTests(faqs))

    // 3. 生成综合报告
    await generateTestReport(testSuites, Date.now() - startTime)

    // 4. 显示测试结果摘要
    displayTestSummary(testSuites)

    // 5. 运行性能监控
    console.log('\n📊 运行性能监控...')
    const metrics = await performanceMonitor.collectMetrics(faqs)
    const report = performanceMonitor.generateReport(metrics)
    await performanceMonitor.savePerformanceData(report)
    
    console.log(`✅ 性能报告已保存到 src/data/performance/`)
    console.log(`📈 平均SEO评分: ${metrics.seoScores.average}/100`)
    console.log(`⚠️  发现 ${report.alerts.length} 个问题`)
    console.log(`💡 生成 ${report.recommendations.length} 条建议`)

    // 6. 检查是否有失败的测试
    const totalFailed = testSuites.reduce((sum, suite) => sum + suite.failedTests, 0)
    if (totalFailed > 0) {
      console.log(`\n❌ 测试失败: ${totalFailed} 个测试未通过`)
      process.exit(1)
    } else {
      console.log('\n✅ 所有测试通过!')
    }

  } catch (error) {
    console.error('❌ 测试执行失败:', error)
    process.exit(1)
  }
}

/**
 * 加载测试数据
 */
async function loadTestData(): Promise<FAQ[]> {
  const faqs: FAQ[] = []
  const generatedDir = path.join(process.cwd(), 'src/data/generated-faqs')
  
  if (!fs.existsSync(generatedDir)) {
    return faqs
  }
  
  const sources = fs.readdirSync(generatedDir)
  
  for (const source of sources) {
    const sourcePath = path.join(generatedDir, source)
    if (fs.statSync(sourcePath).isDirectory()) {
      const files = fs.readdirSync(sourcePath)
        .filter(file => file.endsWith('.json') && file !== 'index.json')
      
      for (const file of files) {
        try {
          const filePath = path.join(sourcePath, file)
          const faqData = JSON.parse(fs.readFileSync(filePath, 'utf8'))
          faqs.push(faqData)
        } catch (error) {
          console.warn(`⚠️  无法加载FAQ文件: ${file}`)
        }
      }
    }
  }
  
  return faqs
}

/**
 * 数据完整性测试
 */
async function runDataIntegrityTests(faqs: FAQ[]): Promise<TestSuite> {
  console.log('\n🔍 运行数据完整性测试...')
  const tests: TestResult[] = []
  const startTime = Date.now()

  // 测试1: FAQ基本字段完整性
  const basicFieldsTest = await runTest('FAQ基本字段完整性', async () => {
    const missingFields: string[] = []
    
    faqs.forEach((faq, index) => {
      if (!faq.id) missingFields.push(`FAQ ${index}: 缺少id`)
      if (!faq.title) missingFields.push(`FAQ ${index}: 缺少title`)
      if (!faq.question) missingFields.push(`FAQ ${index}: 缺少question`)
      if (!faq.answer) missingFields.push(`FAQ ${index}: 缺少answer`)
      if (!faq.category) missingFields.push(`FAQ ${index}: 缺少category`)
    })

    if (missingFields.length > 0) {
      throw new Error(`发现 ${missingFields.length} 个字段缺失: ${missingFields.slice(0, 3).join(', ')}`)
    }

    return `所有 ${faqs.length} 个FAQ的基本字段完整`
  })
  tests.push(basicFieldsTest)

  // 测试2: SEO字段完整性
  const seoFieldsTest = await runTest('SEO字段完整性', async () => {
    const missingSEO = faqs.filter(faq => !faq.seo?.title || !faq.seo?.description)
    
    if (missingSEO.length > 0) {
      return {
        status: 'warning' as const,
        message: `${missingSEO.length} 个FAQ缺少SEO字段`
      }
    }

    return `所有FAQ包含完整的SEO字段`
  })
  tests.push(seoFieldsTest)

  // 测试3: 长尾关键词数据
  const keywordTest = await runTest('长尾关键词数据', async () => {
    const withKeywords = faqs.filter(faq => faq.longTailKeywords && faq.longTailKeywords.length > 0)
    
    if (withKeywords.length === 0) {
      throw new Error('没有FAQ包含长尾关键词数据')
    }

    return `${withKeywords.length}/${faqs.length} 个FAQ包含长尾关键词`
  })
  tests.push(keywordTest)

  return createTestSuite('数据完整性测试', tests, Date.now() - startTime)
}

/**
 * SEO优化测试
 */
async function runSEOOptimizationTests(faqs: FAQ[]): Promise<TestSuite> {
  console.log('\n🎯 运行SEO优化测试...')
  const tests: TestResult[] = []
  const startTime = Date.now()

  // 测试1: SEO优化功能
  const optimizationTest = await runTest('SEO优化功能', async () => {
    const testFAQ = faqs[0]
    const seoData = seoOptimizer.optimizeFAQPage(testFAQ, 'zh')
    
    if (!seoData.metaTags || seoData.metaTags.length === 0) {
      throw new Error('SEO优化未生成meta标签')
    }
    
    if (!seoData.structuredData || seoData.structuredData.length === 0) {
      throw new Error('SEO优化未生成结构化数据')
    }

    return `生成了 ${seoData.metaTags.length} 个meta标签和 ${seoData.structuredData.length} 个结构化数据`
  })
  tests.push(optimizationTest)

  // 测试2: 关键词密度分析
  const keywordDensityTest = await runTest('关键词密度分析', async () => {
    const testFAQ = faqs[0]
    const seoData = seoOptimizer.optimizeFAQPage(testFAQ, 'zh')
    
    if (!seoData.keywordDensity.focusKeyword) {
      throw new Error('未识别主关键词')
    }
    
    if (seoData.keywordDensity.density <= 0) {
      throw new Error('关键词密度计算错误')
    }

    return `主关键词: ${seoData.keywordDensity.focusKeyword}, 密度: ${seoData.keywordDensity.density}%`
  })
  tests.push(keywordDensityTest)

  // 测试3: 性能报告生成
  const reportTest = await runTest('性能报告生成', async () => {
    const testFAQ = faqs[0]
    const report = seoOptimizer.generatePerformanceReport(testFAQ, 'zh')
    
    if (!report.seoScore || report.seoScore < 0 || report.seoScore > 100) {
      throw new Error('SEO评分计算错误')
    }

    return `SEO评分: ${report.seoScore}/100, 问题: ${report.issues.length} 个`
  })
  tests.push(reportTest)

  return createTestSuite('SEO优化测试', tests, Date.now() - startTime)
}

/**
 * 内部链接测试
 */
async function runInternalLinkingTests(faqs: FAQ[]): Promise<TestSuite> {
  console.log('\n🔗 运行内部链接测试...')
  const tests: TestResult[] = []
  const startTime = Date.now()

  // 初始化内部链接索引
  internalLinkingManager.initializeIndex(faqs)

  // 测试1: 索引初始化
  const indexTest = await runTest('索引初始化', async () => {
    const testFAQ = faqs[0]
    const strategy = internalLinkingManager.generateLinkingStrategy(testFAQ, 'zh')
    
    if (!strategy) {
      throw new Error('链接策略生成失败')
    }

    return `生成了 ${strategy.relatedFAQs.length} 个相关链接`
  })
  tests.push(indexTest)

  // 测试2: 内容链接插入
  const linkInsertionTest = await runTest('内容链接插入', async () => {
    const testFAQ = faqs[0]
    const linkedContent = internalLinkingManager.insertLinksInContent(testFAQ.answer, testFAQ, 'zh')
    
    if (linkedContent === testFAQ.answer) {
      return {
        status: 'warning' as const,
        message: '未在内容中插入任何链接'
      }
    }

    const linkCount = (linkedContent.match(/<a /g) || []).length
    return `在内容中插入了 ${linkCount} 个链接`
  })
  tests.push(linkInsertionTest)

  return createTestSuite('内部链接测试', tests, Date.now() - startTime)
}

/**
 * URL结构测试
 */
async function runURLStructureTests(faqs: FAQ[]): Promise<TestSuite> {
  console.log('\n🌐 运行URL结构测试...')
  const tests: TestResult[] = []
  const startTime = Date.now()

  // 测试1: URL生成
  const urlGenerationTest = await runTest('URL生成', async () => {
    const testFAQ = faqs[0]
    const urlStructure = urlStructureManager.generateFAQURLStructure(testFAQ, 'zh')
    
    if (!urlStructure.canonicalUrl) {
      throw new Error('未生成canonical URL')
    }
    
    if (!urlStructure.breadcrumbs || urlStructure.breadcrumbs.length === 0) {
      throw new Error('未生成面包屑导航')
    }

    return `生成URL: ${urlStructure.canonicalUrl}, 面包屑: ${urlStructure.breadcrumbs.length} 级`
  })
  tests.push(urlGenerationTest)

  // 测试2: 站点地图URL
  const sitemapUrlTest = await runTest('站点地图URL', async () => {
    const sitemapUrls = urlStructureManager.generateSitemapUrls(faqs)
    
    if (sitemapUrls.length === 0) {
      throw new Error('未生成站点地图URL')
    }

    return `生成了 ${sitemapUrls.length} 个站点地图URL`
  })
  tests.push(sitemapUrlTest)

  return createTestSuite('URL结构测试', tests, Date.now() - startTime)
}

/**
 * 站点地图测试
 */
async function runSitemapTests(faqs: FAQ[]): Promise<TestSuite> {
  console.log('\n🗺️  运行站点地图测试...')
  const tests: TestResult[] = []
  const startTime = Date.now()

  // 测试1: 站点地图文件存在性
  const fileExistenceTest = await runTest('站点地图文件存在性', async () => {
    const publicDir = path.join(process.cwd(), 'public')
    const requiredFiles = ['sitemap.xml', 'sitemap-main.xml', 'robots.txt']
    const missingFiles: string[] = []

    requiredFiles.forEach(file => {
      const filePath = path.join(publicDir, file)
      if (!fs.existsSync(filePath)) {
        missingFiles.push(file)
      }
    })

    if (missingFiles.length > 0) {
      throw new Error(`缺少站点地图文件: ${missingFiles.join(', ')}`)
    }

    return `所有必需的站点地图文件都存在`
  })
  tests.push(fileExistenceTest)

  // 测试2: XML格式验证
  const xmlValidationTest = await runTest('XML格式验证', async () => {
    const publicDir = path.join(process.cwd(), 'public')
    const sitemapPath = path.join(publicDir, 'sitemap.xml')
    
    if (!fs.existsSync(sitemapPath)) {
      throw new Error('sitemap.xml不存在')
    }

    const content = fs.readFileSync(sitemapPath, 'utf8')
    
    if (!content.includes('<?xml version="1.0"')) {
      throw new Error('XML声明缺失')
    }
    
    if (!content.includes('<sitemapindex')) {
      throw new Error('站点地图索引格式错误')
    }

    return 'XML格式验证通过'
  })
  tests.push(xmlValidationTest)

  return createTestSuite('站点地图测试', tests, Date.now() - startTime)
}

/**
 * 性能测试
 */
async function runPerformanceTests(faqs: FAQ[]): Promise<TestSuite> {
  console.log('\n⚡ 运行性能测试...')
  const tests: TestResult[] = []
  const startTime = Date.now()

  // 测试1: 性能指标收集
  const metricsTest = await runTest('性能指标收集', async () => {
    const metrics = await performanceMonitor.collectMetrics(faqs)
    
    if (!metrics.seoScores || !metrics.contentQuality) {
      throw new Error('性能指标收集失败')
    }

    return `收集了完整的性能指标数据`
  })
  tests.push(metricsTest)

  // 测试2: 警报生成
  const alertsTest = await runTest('警报生成', async () => {
    const metrics = await performanceMonitor.collectMetrics(faqs)
    const alerts = performanceMonitor.generateAlerts(metrics)
    
    return `生成了 ${alerts.length} 个性能警报`
  })
  tests.push(alertsTest)

  return createTestSuite('性能测试', tests, Date.now() - startTime)
}

/**
 * 内容质量测试
 */
async function runContentQualityTests(faqs: FAQ[]): Promise<TestSuite> {
  console.log('\n📝 运行内容质量测试...')
  const tests: TestResult[] = []
  const startTime = Date.now()

  // 测试1: 内容长度检查
  const lengthTest = await runTest('内容长度检查', async () => {
    const shortContent = faqs.filter(faq => faq.answer.length < 100)
    
    if (shortContent.length > faqs.length * 0.5) {
      return {
        status: 'warning' as const,
        message: `${shortContent.length} 个FAQ内容过短`
      }
    }

    return `内容长度检查通过`
  })
  tests.push(lengthTest)

  // 测试2: 重复内容检查
  const duplicateTest = await runTest('重复内容检查', async () => {
    const contentHashes = new Set<string>()
    let duplicates = 0

    faqs.forEach(faq => {
      const hash = faq.answer.substring(0, 100) // 简化的重复检测
      if (contentHashes.has(hash)) {
        duplicates++
      } else {
        contentHashes.add(hash)
      }
    })

    if (duplicates > 0) {
      return {
        status: 'warning' as const,
        message: `发现 ${duplicates} 个可能的重复内容`
      }
    }

    return '未发现重复内容'
  })
  tests.push(duplicateTest)

  return createTestSuite('内容质量测试', tests, Date.now() - startTime)
}

/**
 * 运行单个测试
 */
async function runTest(name: string, testFn: () => Promise<string | { status: 'warning', message: string }>): Promise<TestResult> {
  const startTime = Date.now()
  
  try {
    const result = await testFn()
    const duration = Date.now() - startTime
    
    if (typeof result === 'string') {
      return {
        name,
        status: 'pass',
        message: result,
        duration
      }
    } else {
      return {
        name,
        status: result.status,
        message: result.message,
        duration
      }
    }
  } catch (error) {
    return {
      name,
      status: 'fail',
      message: error instanceof Error ? error.message : '未知错误',
      duration: Date.now() - startTime
    }
  }
}

/**
 * 创建测试套件
 */
function createTestSuite(name: string, tests: TestResult[], duration: number): TestSuite {
  const passedTests = tests.filter(t => t.status === 'pass').length
  const failedTests = tests.filter(t => t.status === 'fail').length
  const warningTests = tests.filter(t => t.status === 'warning').length

  return {
    name,
    tests,
    totalTests: tests.length,
    passedTests,
    failedTests,
    warningTests,
    duration
  }
}

/**
 * 显示测试摘要
 */
function displayTestSummary(testSuites: TestSuite[]) {
  console.log('\n' + '='.repeat(60))
  console.log('📊 测试结果摘要')
  console.log('='.repeat(60))

  let totalTests = 0
  let totalPassed = 0
  let totalFailed = 0
  let totalWarnings = 0

  testSuites.forEach(suite => {
    const status = suite.failedTests > 0 ? '❌' : suite.warningTests > 0 ? '⚠️' : '✅'
    console.log(`${status} ${suite.name}: ${suite.passedTests}/${suite.totalTests} 通过 (${suite.duration}ms)`)
    
    if (suite.failedTests > 0 || suite.warningTests > 0) {
      suite.tests.forEach(test => {
        if (test.status === 'fail') {
          console.log(`  ❌ ${test.name}: ${test.message}`)
        } else if (test.status === 'warning') {
          console.log(`  ⚠️  ${test.name}: ${test.message}`)
        }
      })
    }

    totalTests += suite.totalTests
    totalPassed += suite.passedTests
    totalFailed += suite.failedTests
    totalWarnings += suite.warningTests
  })

  console.log('='.repeat(60))
  console.log(`总计: ${totalPassed}/${totalTests} 通过, ${totalFailed} 失败, ${totalWarnings} 警告`)
  
  if (totalFailed === 0) {
    console.log('🎉 所有关键测试通过!')
  }
}

/**
 * 生成测试报告
 */
async function generateTestReport(testSuites: TestSuite[], totalDuration: number) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalSuites: testSuites.length,
      totalTests: testSuites.reduce((sum, suite) => sum + suite.totalTests, 0),
      totalPassed: testSuites.reduce((sum, suite) => sum + suite.passedTests, 0),
      totalFailed: testSuites.reduce((sum, suite) => sum + suite.failedTests, 0),
      totalWarnings: testSuites.reduce((sum, suite) => sum + suite.warningTests, 0),
      duration: totalDuration
    },
    testSuites
  }

  const outputDir = path.join(process.cwd(), 'src/data/test-reports')
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true })
  }

  const timestamp = new Date().toISOString().split('T')[0]
  const filename = `test-report-${timestamp}.json`
  const filePath = path.join(outputDir, filename)

  fs.writeFileSync(filePath, JSON.stringify(report, null, 2), 'utf8')
  
  // 保存最新报告
  const latestPath = path.join(outputDir, 'latest-test-report.json')
  fs.writeFileSync(latestPath, JSON.stringify(report, null, 2), 'utf8')

  console.log(`📄 测试报告已保存到: ${filePath}`)
}

// 运行主程序
main().catch(error => {
  console.error('❌ 测试执行失败:', error)
  process.exit(1)
})
