#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 由于没有SVG转PNG工具，我将创建简单的HTML文件来手动转换
// 或者提供用户可以使用的在线工具链接

const iconsDir = path.join(__dirname, '../public/icons');

// 创建一个HTML文件用于手动转换SVG到PNG
const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>G6PD Icon Converter</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-container { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .icon-preview { display: inline-block; margin: 10px; text-align: center; }
        canvas { border: 1px solid #ddd; margin: 5px; }
        button { padding: 10px 20px; margin: 5px; background: #dc2626; color: white; border: none; cursor: pointer; }
        button:hover { background: #b91c1c; }
    </style>
</head>
<body>
    <h1>G6PD 图标转换器</h1>
    <p>使用此页面将SVG图标转换为PNG格式</p>

    <div class="icon-container">
        <h3>16x16 图标 (简化版)</h3>
        <div class="icon-preview">
            <canvas id="canvas16" width="16" height="16"></canvas>
            <br><button onclick="downloadPNG('canvas16', 'favicon-16x16.png')">下载 PNG</button>
        </div>
    </div>

    <div class="icon-container">
        <h3>32x32 图标 (简化版)</h3>
        <div class="icon-preview">
            <canvas id="canvas32" width="32" height="32"></canvas>
            <br><button onclick="downloadPNG('canvas32', 'favicon-32x32.png')">下载 PNG</button>
        </div>
    </div>

    <div class="icon-container">
        <h3>48x48 图标</h3>
        <div class="icon-preview">
            <canvas id="canvas48" width="48" height="48"></canvas>
            <br><button onclick="downloadPNG('canvas48', 'favicon-48x48.png')">下载 PNG</button>
        </div>
    </div>

    <div class="icon-container">
        <h3>180x180 图标 (Apple Touch)</h3>
        <div class="icon-preview">
            <canvas id="canvas180" width="180" height="180"></canvas>
            <br><button onclick="downloadPNG('canvas180', 'apple-touch-icon.png')">下载 PNG</button>
        </div>
    </div>

    <div class="icon-container">
        <h3>192x192 图标 (Android Chrome)</h3>
        <div class="icon-preview">
            <canvas id="canvas192" width="192" height="192"></canvas>
            <br><button onclick="downloadPNG('canvas192', 'android-chrome-192x192.png')">下载 PNG</button>
        </div>
    </div>

    <div class="icon-container">
        <h3>512x512 图标 (Android Chrome)</h3>
        <div class="icon-preview">
            <canvas id="canvas512" width="512" height="512"></canvas>
            <br><button onclick="downloadPNG('canvas512', 'android-chrome-512x512.png')">下载 PNG</button>
        </div>
    </div>

    <script>
        // 绘制血滴图标的函数
        function drawBloodDrop(ctx, size, simplified = false) {
            const centerX = size / 2;
            const centerY = size / 2;

            // 清除画布
            ctx.clearRect(0, 0, size, size);

            if (!simplified && size >= 48) {
                // 绘制背景圆形
                ctx.fillStyle = '#FFFFFF';
                ctx.strokeStyle = '#E5E7EB';
                ctx.lineWidth = Math.max(1, size * 0.008);
                ctx.beginPath();
                ctx.arc(centerX, centerY, size * 0.45, 0, 2 * Math.PI);
                ctx.fill();
                ctx.stroke();
            }

            // 创建血滴渐变
            const gradient = ctx.createLinearGradient(0, 0, 0, size);
            gradient.addColorStop(0, '#EF4444');
            gradient.addColorStop(1, '#DC2626');

            // 绘制血滴形状
            ctx.fillStyle = gradient;
            ctx.beginPath();

            const startY = simplified ? size * 0.15 : size * 0.2;
            const endY = simplified ? size * 0.98 : size * 0.98;
            const width = simplified ? size * 0.7 : size * 0.6;

            ctx.moveTo(centerX, startY);
            ctx.bezierCurveTo(
                centerX + width * 0.3, startY + size * 0.2,
                centerX + width * 0.4, startY + size * 0.4,
                centerX + width * 0.3, startY + size * 0.6
            );
            ctx.bezierCurveTo(
                centerX + width * 0.2, endY - size * 0.1,
                centerX - width * 0.2, endY - size * 0.1,
                centerX - width * 0.3, startY + size * 0.6
            );
            ctx.bezierCurveTo(
                centerX - width * 0.4, startY + size * 0.4,
                centerX - width * 0.3, startY + size * 0.2,
                centerX, startY
            );
            ctx.fill();

            if (!simplified && size >= 48) {
                // 绘制高光
                ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
                ctx.beginPath();
                ctx.ellipse(centerX - size * 0.08, centerY - size * 0.1, size * 0.08, size * 0.12, 0, 0, 2 * Math.PI);
                ctx.fill();

                // 绘制G6PD文字
                const fontSize = Math.max(8, size * 0.12);
                ctx.fillStyle = '#FFFFFF';
                ctx.font = \`bold \${fontSize}px Arial, sans-serif\`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('G6PD', centerX, centerY + size * 0.05);

                // 绘制医疗十字
                const crossSize = size * 0.08;
                const crossX = size * 0.85;
                const crossY = size * 0.85;
                ctx.fillStyle = 'rgba(220, 38, 38, 0.6)';
                ctx.fillRect(crossX - crossSize/2, crossY - crossSize/6, crossSize, crossSize/3);
                ctx.fillRect(crossX - crossSize/6, crossY - crossSize/2, crossSize/3, crossSize);
            }
        }

        // 下载PNG函数
        function downloadPNG(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        // 初始化所有画布
        window.onload = function() {
            drawBloodDrop(document.getElementById('canvas16').getContext('2d'), 16, true);
            drawBloodDrop(document.getElementById('canvas32').getContext('2d'), 32, true);
            drawBloodDrop(document.getElementById('canvas48').getContext('2d'), 48, false);
            drawBloodDrop(document.getElementById('canvas180').getContext('2d'), 180, false);
            drawBloodDrop(document.getElementById('canvas192').getContext('2d'), 192, false);
            drawBloodDrop(document.getElementById('canvas512').getContext('2d'), 512, false);
        };
    </script>
</body>
</html>`;

// 保存HTML文件
const htmlPath = path.join(iconsDir, 'icon-converter.html');
fs.writeFileSync(htmlPath, htmlContent);

console.log('✅ 创建了图标转换器: public/icons/icon-converter.html');
console.log('📝 请在浏览器中打开此文件来下载PNG图标');
console.log('🌐 文件路径: file://' + htmlPath);

// 创建临时的PNG占位符文件（用于测试）
const createPlaceholderPNG = (size, filename) => {
    // 这里创建一个简单的数据URL作为占位符
    // 实际的PNG文件需要通过HTML转换器生成
    const placeholder = `<!-- PNG placeholder for ${filename} (${size}x${size}) -->`;
    const placeholderPath = path.join(iconsDir, filename);

    // 创建一个简单的红色方块作为临时图标
    const svgPlaceholder = `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
        <rect width="${size}" height="${size}" fill="#dc2626"/>
        <text x="${size/2}" y="${size/2}" text-anchor="middle" dominant-baseline="middle" fill="white" font-size="${size/8}">G6PD</text>
    </svg>`;

    fs.writeFileSync(placeholderPath.replace('.png', '-placeholder.svg'), svgPlaceholder);
};

// 创建占位符文件
const sizes = [
    { size: 16, name: 'favicon-16x16.png' },
    { size: 32, name: 'favicon-32x32.png' },
    { size: 48, name: 'favicon-48x48.png' },
    { size: 180, name: 'apple-touch-icon.png' },
    { size: 192, name: 'android-chrome-192x192.png' },
    { size: 512, name: 'android-chrome-512x512.png' }
];

sizes.forEach(({ size, name }) => {
    createPlaceholderPNG(size, name);
});

console.log('✅ 创建了SVG占位符文件');
console.log('\n🔧 下一步操作:');
console.log('1. 在浏览器中打开 icon-converter.html');
console.log('2. 下载所有PNG文件到 public/icons/ 目录');
console.log('3. 运行配置更新脚本');