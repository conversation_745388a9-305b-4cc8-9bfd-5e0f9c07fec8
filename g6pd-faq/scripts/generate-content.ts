#!/usr/bin/env tsx

import { keywordProcessor } from '../src/lib/keyword-processor'
import { contentGenerator } from '../src/lib/content-generator'
import { FAQ } from '../src/lib/types'
import fs from 'fs'
import path from 'path'

/**
 * 内容生成脚本
 * 基于处理后的关键词批量生成FAQ内容
 */

interface GenerationOptions {
  sourceId?: string
  limit?: number
  category?: string
  minSearchVolume?: number
  outputFormat?: 'json' | 'markdown'
  dryRun?: boolean
}

async function main() {
  const args = process.argv.slice(2)
  const options = parseArgs(args)
  
  console.log('🚀 开始生成FAQ内容...')
  console.log('配置:', options)
  
  try {
    // 初始化内容生成器
    await contentGenerator.initializeTemplates()
    
    // 获取要处理的数据源
    const sources = options.sourceId ? [options.sourceId] : ['chinese-medicine-keywords', 'oral-solution-keywords']
    
    for (const sourceId of sources) {
      console.log(`\n📝 处理数据源: ${sourceId}`)
      await generateContentForSource(sourceId, options)
    }
    
    console.log('\n✅ 内容生成完成!')
    
  } catch (error) {
    console.error('❌ 内容生成失败:', error)
    process.exit(1)
  }
}

/**
 * 为指定数据源生成内容
 */
async function generateContentForSource(sourceId: string, options: GenerationOptions) {
  // 加载处理后的关键词
  const keywordsFile = path.join(process.cwd(), 'src/data/processed-keywords', `${sourceId}.json`)
  
  if (!fs.existsSync(keywordsFile)) {
    console.log(`⚠️  关键词文件不存在: ${keywordsFile}`)
    return
  }
  
  const keywordsData = JSON.parse(fs.readFileSync(keywordsFile, 'utf8'))
  let keywords = keywordsData.keywords || []
  
  // 应用过滤条件
  if (options.category) {
    keywords = keywordProcessor.getKeywordsByCategory(sourceId, options.category)
  }
  
  if (options.minSearchVolume) {
    keywords = keywords.filter((kw: any) => kw.searchVolume >= options.minSearchVolume!)
  }
  
  if (options.limit) {
    keywords = keywords.slice(0, options.limit)
  }
  
  console.log(`  找到 ${keywords.length} 个符合条件的关键词`)
  
  // 确定模板
  const templateMap: Record<string, string> = {
    'chinese-medicine-keywords': 'chinese-medicine-faq',
    'oral-solution-keywords': 'oral-solution-faq'
  }
  
  const templateId = templateMap[sourceId]
  if (!templateId) {
    console.log(`⚠️  未找到对应的模板: ${sourceId}`)
    return
  }
  
  // 生成内容
  const generatedFAQs: FAQ[] = []
  let successCount = 0
  let errorCount = 0
  
  for (let i = 0; i < keywords.length; i++) {
    const keyword = keywords[i]
    const progress = Math.round(((i + 1) / keywords.length) * 100)
    
    try {
      console.log(`  [${progress}%] 生成: ${keyword.keyword}`)
      
      if (options.dryRun) {
        console.log(`    🔍 预览模式 - 跳过实际生成`)
        continue
      }
      
      const faq = await contentGenerator.generateFAQFromKeyword(keyword, templateId)
      generatedFAQs.push(faq)
      
      console.log(`    ✓ 质量评分: ${faq.qualityScore}/100`)
      console.log(`    ✓ 内容长度: ${faq.answer.length} 字符`)
      
      successCount++
      
    } catch (error) {
      console.error(`    ❌ 生成失败: ${error}`)
      errorCount++
    }
  }
  
  if (!options.dryRun && generatedFAQs.length > 0) {
    // 保存生成的内容
    await saveGeneratedContent(sourceId, generatedFAQs, options.outputFormat || 'json')
  }
  
  console.log(`  📊 生成统计: 成功 ${successCount}, 失败 ${errorCount}`)
}

/**
 * 保存生成的内容
 */
async function saveGeneratedContent(sourceId: string, faqs: FAQ[], format: 'json' | 'markdown') {
  const outputDir = path.join(process.cwd(), 'src/data/generated-faqs', sourceId)
  
  // 确保目录存在
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true })
  }
  
  if (format === 'json') {
    // 保存为JSON文件
    for (const faq of faqs) {
      const fileName = `${faq.slug}.json`
      const filePath = path.join(outputDir, fileName)
      fs.writeFileSync(filePath, JSON.stringify(faq, null, 2), 'utf8')
    }
    
    // 保存索引文件
    const indexData = {
      sourceId,
      generatedAt: new Date().toISOString(),
      totalFAQs: faqs.length,
      faqs: faqs.map(faq => ({
        id: faq.id,
        slug: faq.slug,
        title: faq.title,
        category: faq.category,
        subcategory: faq.subcategory,
        searchVolume: faq.searchVolume,
        qualityScore: faq.qualityScore
      }))
    }
    
    const indexPath = path.join(outputDir, 'index.json')
    fs.writeFileSync(indexPath, JSON.stringify(indexData, null, 2), 'utf8')
    
  } else if (format === 'markdown') {
    // 保存为Markdown文件
    for (const faq of faqs) {
      const frontMatter = {
        id: faq.id,
        title: faq.title,
        category: faq.category,
        subcategory: faq.subcategory,
        tags: faq.tags,
        difficulty: faq.difficulty,
        priority: faq.priority,
        lastUpdated: faq.lastUpdated,
        locale: faq.locale,
        searchVolume: faq.searchVolume,
        qualityScore: faq.qualityScore,
        autoGenerated: faq.autoGenerated
      }
      
      const content = `---
${Object.entries(frontMatter).map(([key, value]) => 
  `${key}: ${Array.isArray(value) ? JSON.stringify(value) : value}`
).join('\n')}
---

# ${faq.question}

${faq.answer}
`
      
      const fileName = `${faq.slug}.md`
      const filePath = path.join(outputDir, fileName)
      fs.writeFileSync(filePath, content, 'utf8')
    }
  }
  
  console.log(`  💾 内容已保存到: ${outputDir}`)
}

/**
 * 解析命令行参数
 */
function parseArgs(args: string[]): GenerationOptions {
  const options: GenerationOptions = {
    outputFormat: 'json',
    dryRun: false
  }
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i]
    
    switch (arg) {
      case '--source':
        options.sourceId = args[++i]
        break
      case '--limit':
        options.limit = parseInt(args[++i])
        break
      case '--category':
        options.category = args[++i]
        break
      case '--min-volume':
        options.minSearchVolume = parseInt(args[++i])
        break
      case '--format':
        options.outputFormat = args[++i] as 'json' | 'markdown'
        break
      case '--dry-run':
        options.dryRun = true
        break
      case '--help':
        showHelp()
        process.exit(0)
        break
    }
  }
  
  return options
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
内容生成脚本

用法:
  npm run generate-content [选项]

选项:
  --source <id>           指定数据源ID (chinese-medicine-keywords | oral-solution-keywords)
  --limit <number>        限制生成数量
  --category <category>   按分类过滤
  --min-volume <number>   最小搜索量过滤
  --format <format>       输出格式 (json | markdown)
  --dry-run              预览模式，不实际生成内容
  --help                 显示帮助信息

示例:
  npm run generate-content -- --source chinese-medicine-keywords --limit 10
  npm run generate-content -- --min-volume 10000 --format markdown
  npm run generate-content -- --dry-run
`)
}

// 运行主程序
main().catch(error => {
  console.error('❌ 脚本执行失败:', error)
  process.exit(1)
})
