/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd.site',
  generateRobotsTxt: true,
  generateIndexSitemap: false,
  exclude: ['*'], // Exclude all pages - we use custom hierarchical sitemap generation

  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: ['/api/', '/_next/', '/admin/'],
      },
    ],
    additionalSitemaps: [
      'https://g6pd.site/sitemap.xml', // Main hierarchical sitemap index
      'https://g6pd.site/sitemap_static.xml',
      'https://g6pd.site/sitemap_categories.xml',
      'https://g6pd.site/sitemap_programmatic.xml'
    ],
  },
}
