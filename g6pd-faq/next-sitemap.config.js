const fs = require('fs')
const path = require('path')

// 加载程序化SEO生成的FAQ内容
function loadGeneratedFAQs() {
  const faqs = []
  const generatedFaqsPath = path.join(process.cwd(), 'src/data/generated-faqs')

  if (!fs.existsSync(generatedFaqsPath)) {
    console.warn('Generated FAQs directory not found:', generatedFaqsPath)
    return faqs
  }

  try {
    // 遍历所有子目录
    for (const subDir of fs.readdirSync(generatedFaqsPath)) {
      const subDirPath = path.join(generatedFaqsPath, subDir)
      if (fs.statSync(subDirPath).isDirectory()) {
        // 遍历子目录中的JSON文件
        for (const file of fs.readdirSync(subDirPath).filter(f => f.endsWith('.json') && f !== 'index.json')) {
          try {
            const filePath = path.join(subDirPath, file)
            const faqData = JSON.parse(fs.readFileSync(filePath, 'utf8'))
            faqs.push(faqData)
          } catch (error) {
            console.warn(`Failed to load FAQ file: ${file}`, error)
          }
        }
      }
    }
  } catch (error) {
    console.warn('Error loading generated FAQs:', error)
  }

  return faqs
}

/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd.site',
  generateRobotsTxt: true,
  generateIndexSitemap: false,
  exclude: ['/api/*', '/_next/*', '/admin/*'],

  // 自定义sitemap生成
  additionalPaths: async (config) => {
    const paths = []

    // 加载程序化SEO生成的FAQ
    const generatedFaqs = loadGeneratedFAQs()
    console.log(`Loading ${generatedFaqs.length} programmatic SEO FAQs into next-sitemap`)

    // 为每个生成的FAQ添加路径
    generatedFaqs.forEach(faq => {
      const locale = faq.locale || 'zh'
      const category = faq.category === 'medications' ? '/medications' : `/${faq.category}`
      const subcategory = faq.subcategory ? `/${faq.subcategory}` : ''
      const url = `/${locale}/faq${category}${subcategory}/${encodeURIComponent(faq.slug)}`

      paths.push({
        loc: url,
        lastmod: faq.lastUpdated ? new Date(faq.lastUpdated).toISOString() : new Date().toISOString(),
        changefreq: 'monthly',
        priority: faq.priority ? Math.min(faq.priority / 100, 0.9) : 0.7,
      })
    })

    console.log(`Generated ${paths.length} additional sitemap paths for programmatic SEO`)
    return paths
  },

  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: ['/api/', '/_next/', '/admin/'],
      },
    ],
  },
}
