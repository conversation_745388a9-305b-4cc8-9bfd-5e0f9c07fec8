/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.NEXT_PUBLIC_BASE_URL || 'https://g6pd.site',
  generateRobotsTxt: true,
  generateIndexSitemap: false,
  exclude: ['*'], // Exclude all pages from next-sitemap generation
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: ['/api/', '/_next/', '/admin/'],
      },
    ],
    additionalSitemaps: [
      'https://g6pd.site/sitemap.xml', // Next.js built-in sitemap
    ],
  },
}
