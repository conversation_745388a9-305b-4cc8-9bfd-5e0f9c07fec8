<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 血滴渐变 -->
    <linearGradient id="bloodGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#EF4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DC2626;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影效果 -->
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="4" dy="8" stdDeviation="8" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="256" cy="256" r="240" fill="#FFFFFF" stroke="#E5E7EB" stroke-width="4"/>
  
  <!-- 血滴主体 -->
  <path d="M 256 80 
           C 256 80, 320 140, 360 200
           C 380 230, 380 270, 360 300
           C 340 340, 300 360, 256 360
           C 212 360, 172 340, 152 300
           C 132 270, 132 230, 152 200
           C 192 140, 256 80, 256 80 Z" 
        fill="url(#bloodGradient)" 
        filter="url(#dropShadow)"/>
  
  <!-- 血滴高光效果 -->
  <ellipse cx="230" cy="160" rx="25" ry="35" fill="#FFFFFF" opacity="0.3"/>
  
  <!-- G6PD 文字 -->
  <text x="256" y="240" 
        font-family="Arial, sans-serif" 
        font-size="48" 
        font-weight="bold" 
        text-anchor="middle" 
        fill="#FFFFFF">G6PD</text>
  
  <!-- 医疗十字符号 -->
  <g transform="translate(380, 380)" opacity="0.6">
    <rect x="-12" y="-4" width="24" height="8" fill="#DC2626"/>
    <rect x="-4" y="-12" width="8" height="24" fill="#DC2626"/>
  </g>
</svg>
