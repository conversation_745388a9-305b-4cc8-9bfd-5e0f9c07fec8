<svg width="180" height="180" viewBox="0 0 180 180" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bloodGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#EF4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DC2626;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="90" cy="90" r="81" fill="#FFFFFF" stroke="#E5E7EB" stroke-width="1.44"/>
  
  <!-- 血滴主体 -->
  <path d="M 90 36 
           C 90 36, 125.99999999999999 62.99999999999999, 144 99.00000000000001
           C 153 117, 153 135, 144 153
           C 125.99999999999999 171, 108 176.4, 90 176.4
           C 72 176.4, 54 171, 36 153
           C 27 135, 27 117, 36 99.00000000000001
           C 54 62.99999999999999, 90 36, 90 36 Z" 
        fill="url(#bloodGradient)"/>
  
  <!-- 血滴高光 -->
  <ellipse cx="75.6" cy="72" rx="14.4" ry="21.599999999999998" fill="#FFFFFF" opacity="0.3"/>
  
  <!-- G6PD 文字 -->
  <text x="90" y="117" 
        font-family="Arial, sans-serif" 
        font-size="21.599999999999998" 
        font-weight="bold" 
        text-anchor="middle" 
        fill="#FFFFFF">G6PD</text>
  
  <!-- 医疗十字 -->
  <g transform="translate(153, 153)" opacity="0.6">
    <rect x="-7.2" y="-2.4" width="14.4" height="4.8" fill="#DC2626"/>
    <rect x="-2.4" y="-7.2" width="4.8" height="14.4" fill="#DC2626"/>
  </g>
</svg>