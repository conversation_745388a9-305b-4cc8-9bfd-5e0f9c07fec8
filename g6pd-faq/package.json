{"name": "g6pd-faq", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "postbuild": "node scripts/generate-hierarchical-sitemap.js", "deploy": "./scripts/deploy.sh", "deploy:prod": "./scripts/deploy.sh production", "analyze": "ANALYZE=true npm run build", "test": "echo \"No tests specified\" && exit 0", "process-keywords": "tsx scripts/process-keywords.ts", "generate-content": "tsx scripts/generate-content.ts", "test-navigation": "tsx scripts/test-navigation.ts", "test-seo": "tsx scripts/test-seo-optimization.ts", "generate-sitemap": "tsx scripts/generate-sitemap.ts", "generate-hierarchical-sitemap": "node scripts/generate-hierarchical-sitemap.js", "test-all": "tsx scripts/run-comprehensive-tests.ts", "run-all-tasks": "tsx scripts/run-all-tasks.ts", "seo-audit": "tsx scripts/seo-audit.ts"}, "dependencies": {"@heroicons/react": "^2.2.0", "@vercel/analytics": "^1.5.0", "clsx": "^2.1.1", "gray-matter": "^4.0.3", "next": "15.3.4", "next-intl": "^4.3.1", "next-seo": "^6.8.0", "next-sitemap": "^4.2.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}